<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!-- Define properties for log file path and pattern -->
    <property name="LOG_FILE_PATH" value="/var/log/avinyaops/application.log"/>
    <property name="LOG_FILE_PATTERN" value="/var/log/avinyaops/application-%d{yyyy-MM-dd}.%i.log"/>

    <!-- Define the console log pattern -->
    <property name="CONSOLE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%highlight(%-5level)] [%cyan(%thread)] %boldBlue(%logger{36}) - %msg%n"/>

    <!-- Console Appender -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- Rolling File Appender -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- Use the property for the log file -->
        <file>${LOG_FILE_PATH}</file> <!-- Main log file -->

        <!-- Rolling Policy: Log rotation based on time (daily) -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- Use the property for the rolling file pattern -->
            <fileNamePattern>${LOG_FILE_PATTERN}</fileNamePattern> <!-- Daily rotated logs -->
            <maxHistory>30</maxHistory> <!-- Retain logs for 30 days -->
            <totalSizeCap>1GB</totalSizeCap> <!-- Max total size of logs -->
        </rollingPolicy>

        <!-- Encoder for the log file -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Root Logger -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </root>

</configuration>
