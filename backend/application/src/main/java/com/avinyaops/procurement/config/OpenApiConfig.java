package com.avinyaops.procurement.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class OpenApiConfig {

    @Value("${openapi.dev-url}")
    private String devUrl;

    @Value("${openapi.prod-url}")
    private String prodUrl;

    @Bean
    public OpenAPI myOpenAPI() {
        Server devServer = new Server();
        devServer.setUrl(devUrl);
        devServer.setDescription("Server URL in Development environment");

        Server prodServer = new Server();
        prodServer.setUrl(prodUrl);
        prodServer.setDescription("Server URL in Production environment");

        Contact contact = new Contact();
        contact.setEmail("<EMAIL>");
        contact.setName("Avinya Ops Support");
        contact.setUrl("https://www.avinyaops.com");

        License license = new License()
                .name("MIT License")
                .url("https://opensource.org/licenses/MIT");

        Info info = new Info()
                .title("Avinya Ops Procurement API")
                .version("1.0")
                .contact(contact)
                .description("This API exposes endpoints for Avinya Ops Procurement Platform.")
                .termsOfService("https://www.avinyaops.com/terms")
                .license(license);

        SecurityScheme securityScheme = new SecurityScheme()
                .type(SecurityScheme.Type.HTTP)
                .scheme("bearer")
                .bearerFormat("JWT")
                .in(SecurityScheme.In.HEADER)
                .name("Authorization");

        return new OpenAPI()
                .info(info)
                .servers(List.of(devServer, prodServer))
                .addSecurityItem(new SecurityRequirement().addList("bearerAuth"))
                .schemaRequirement("bearerAuth", securityScheme);
    }

//     @Bean
//     public GroupedOpenApi publicApi() {
//         return GroupedOpenApi.builder()
//                 .group("public")
//                 .pathsToMatch("/api/v1/public/**")
//                 .build();
//     }

//     @Bean
//     public GroupedOpenApi adminApi() {
//         return GroupedOpenApi.builder()
//                 .group("admin")
//                 .pathsToMatch("/api/v1/admin/**")
//                 .build();
//     }

//     @Bean
//     public GroupedOpenApi organizationApi() {
//         return GroupedOpenApi.builder()
//                 .group("organization")
//                 .pathsToMatch("/api/v1/organizations/**")
//                 .build();
//     }

//     @Bean
//     public GroupedOpenApi userApi() {
//         return GroupedOpenApi.builder()
//                 .group("user")
//                 .pathsToMatch("/api/v1/users/**")
//                 .build();
//     }
} 