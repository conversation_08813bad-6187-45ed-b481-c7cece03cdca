package com.avinyaops.fileupload.service;

import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.avinyaops.fileupload.dto.UploadFileResponseDto;

public interface FileStorageService {

    /**
     * Uploads a file to storage
     * 
     * @param file       The file to be uploaded
     * @param folderPath The folder path where the file should be stored (e.g.,
     *                   "user/profile-pictures")
     * @return UploadFileResponseDto containing file metadata
     */
    UploadFileResponseDto uploadFile(MultipartFile file, String folderPath);

    /**
     * Uploads multiple files to storage
     * 
     * @param files      The files to be uploaded
     * @param folderPath The folder path where the files should be stored (e.g.,
     *                   "user/profile-pictures")
     * @return List of UploadFileResponseDto containing file metadata
     */
    List<UploadFileResponseDto> uploadFiles(List<MultipartFile> files, String folderPath);

    /**
     * Generates a pre-signed URL for viewing a file
     * 
     * @param fileId The ID of the file to view
     * @return Pre-signed URL for viewing the file
     */
    String generateViewUrl(String fileId);

    /**
     * Deletes a file from storage
     * 
     * @param fileId The ID of the file to delete
     * @return The storage key of the deleted file
     */
    String deleteFile(String fileId);
}