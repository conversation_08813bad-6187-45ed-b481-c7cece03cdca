package com.avinyaops.procurement.organization.designation.service;

import com.avinyaops.procurement.organization.designation.dto.DesignationDTO;
import java.util.List;

public interface DesignationService {
    DesignationDTO createDesignation(DesignationDTO designationDTO);
    DesignationDTO updateDesignation(Long id, DesignationDTO designationDTO);
    void deleteDesignation(Long id);
    DesignationDTO getDesignation(Long id);
    List<DesignationDTO> getAllDesignations();
    List<DesignationDTO> getDesignationsByOrganization(Long organizationId);
} 