# Avinya Snowflake ID Generator

## Overview
The Avinya Snowflake ID Generator is a distributed ID generation system inspired by Twitter's Snowflake algorithm, but optimized for single-server deployment and **JavaScript compatibility**. It generates **47-bit** unique identifiers that are:
- Time-ordered
- Guaranteed unique
- Thread-safe
- High-performance
- **JavaScript Number safe** (guaranteed no precision loss)
- **Conservative bit allocation** to ensure compatibility

## Bit Structure
The **47-bit** ID is composed of two parts (Conservative JavaScript safe):

```
|-- Timestamp (35 bits) --|-- Sequence (12 bits) --|
```

### Original 64-bit Structure (Preserved in Comments)
```
|-- Timestamp (41 bits) --|-- Sequence (12 bits) --|-- Reserved (11 bits) --|
```
*The original 64-bit implementation is preserved in code comments for potential reversion.*

### Components (47-bit Conservative JavaScript Safe Version)
1. **Timestamp (35 bits)**
   - Milliseconds since epoch (01-01-2024 00:00:00 UTC)
   - Provides ~34 years of unique timestamps (sufficient for long-term use)
   - Ensures time-ordered IDs
   - Epoch: 1704067200000L (01-01-2024 00:00:00 UTC)
   - **Reduced from 41 to 35 bits to guarantee JavaScript precision safety**

2. **Sequence (12 bits)**
   - Counter for IDs generated within the same millisecond
   - Supports 4096 (2^12) unique IDs per millisecond
   - Resets to 0 when timestamp changes
   - Handles high concurrency

### Original Components (64-bit Version - Commented Out)
3. **Reserved (11 bits) - REMOVED for JavaScript compatibility**
   - ~~Reserved for future use~~
   - ~~Can be used for:~~
     - ~~Entity type identification~~
     - ~~Shard ID~~
     - ~~Custom flags~~
     - ~~Other business requirements~~
   - **Removed to ensure IDs fit within JavaScript Number.MAX_SAFE_INTEGER (2^53 - 1)**

## Algorithm Details

### ID Generation Process
1. Get current timestamp in milliseconds
2. If timestamp is less than last timestamp, throw exception (clock moved backwards)
3. If timestamp equals last timestamp:
   - Increment sequence counter
   - If sequence overflows, wait for next millisecond
4. If timestamp is greater than last timestamp:
   - Reset sequence to 0
5. Combine components (47-bit Conservative JavaScript safe):
   ```java
   // New 47-bit structure (Conservative JavaScript safe)
   long timestampDelta = (timestamp - epoch) & TIMESTAMP_MASK; // Ensure 35-bit limit
   ID = (timestampDelta << timestampShift) |
        (sequence & sequenceMask)

   // Original 64-bit structure (commented out)
   // ID = ((timestamp - epoch) << timestampShift) |
   //      (sequence << sequenceShift) |
   //      (reserved & reservedMask)
   ```
6. Validate JavaScript safety:
   ```java
   if (id > JAVASCRIPT_MAX_SAFE_INTEGER) {
       throw new IllegalStateException("Generated ID exceeds JavaScript safe integer limit");
   }
   // Additional 47-bit validation
   if (id > ((1L << 47) - 1)) {
       throw new IllegalStateException("Generated ID exceeds 47-bit limit");
   }
   ```

### Thread Safety
- Uses `AtomicLong` for sequence counter
- Synchronized method for ID generation
- Handles concurrent requests safely

### Clock Drift Handling
- Detects and prevents clock drift
- Throws exception if clock moves backwards
- Waits for next millisecond if sequence overflows

## Usage Example
```java
@Entity
public class YourEntity {
    @AvinyaId
    @Id
    private Long id;
    // other fields
}
```

## Performance Characteristics
- Generates IDs at high throughput
- No database dependency
- No network calls
- Minimal memory footprint
- Thread-safe operations

## Limitations
- Depends on system clock accuracy
- Maximum 4096 IDs per millisecond
- **~34 years of unique IDs from epoch** (reduced for JavaScript safety)
- Single-server deployment only
- **Limited to 47 bits for guaranteed JavaScript Number compatibility**
- **No reserved bits available for custom use** (removed for JavaScript safety)

## JavaScript Compatibility
- **IDs are guaranteed to be ≤ 2^47 - 1 (well within JavaScript safe limits)**
- **Conservative 47-bit allocation ensures no precision loss**
- **Safe to use as JavaScript Numbers without any precision loss**
- **Can be serialized to JSON without losing precision**
- **Compatible with JavaScript frameworks and libraries**
- **Tested to prevent precision loss issues like 372048902347554816 → 372048902347554800**

## Future Enhancements
~~The reserved bits (11) can be used for:~~ **REMOVED for JavaScript compatibility**
- ~~Entity type identification~~
- ~~Environment identification~~
- ~~Custom flags~~
- ~~Business-specific requirements~~

**Alternative approaches for extensibility:**
- Use separate fields for entity type identification
- Implement custom ID prefixes or suffixes
- Use database-level metadata for classification
- Consider UUID alternatives if more bits are needed

## Best Practices
1. Ensure system clock is synchronized
2. Monitor sequence overflow
3. Plan for epoch rollover
4. ~~Use reserved bits strategically~~ **No reserved bits in 53-bit version**
5. Consider backup ID generation strategy
6. **JavaScript-specific practices:**
   - Always use the IDs as Numbers in JavaScript (not strings)
   - Verify `Number.isSafeInteger(id)` returns `true` when debugging
   - Use JSON serialization safely without precision loss
   - Avoid manual bit manipulation in JavaScript