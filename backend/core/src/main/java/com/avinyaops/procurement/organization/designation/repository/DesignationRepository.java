package com.avinyaops.procurement.organization.designation.repository;

import com.avinyaops.procurement.organization.designation.model.Designation;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DesignationRepository extends JpaRepository<Designation, Long> {

        @Query("SELECT d FROM Designation d")
        @NonNull
        List<Designation> findAll();

        @Query("SELECT d FROM Designation d WHERE d.id = :id")
        @NonNull
        Optional<Designation> findById(@Param("id") @NonNull Long id);

        @Query("SELECT d FROM Designation d WHERE d.name = :name")
        Optional<Designation> findByName(@Param("name") String name);

        @Query("SELECT CASE WHEN COUNT(d) > 0 THEN true ELSE false END FROM Designation d " +
                        "WHERE d.name = :name")
        boolean existsByName(@Param("name") String name);

        @Query("SELECT d FROM Designation d")
        List<Designation> findAllActive();

        @Query("SELECT d FROM Designation d WHERE d.organization.id = :organizationId")
        List<Designation> findAllByOrganizationId(@Param("organizationId") Long organizationId);

        @Query("SELECT CASE WHEN COUNT(d) > 0 THEN true ELSE false END FROM Designation d " +
                        "WHERE d.name = :name AND d.organization.id = :organizationId")
        boolean existsByNameAndOrganizationId(@Param("name") String name, @Param("organizationId") Long organizationId);
}