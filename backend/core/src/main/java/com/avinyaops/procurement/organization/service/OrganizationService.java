package com.avinyaops.procurement.organization.service;

import java.util.List;

import com.avinyaops.procurement.organization.dto.OrganizationCreateUpdateRequest;
import com.avinyaops.procurement.organization.dto.OrganizationDetailResponse;
import com.avinyaops.procurement.organization.dto.OrganizationShortResponse;
import com.avinyaops.procurement.organization.model.Organization;

public interface OrganizationService {
    OrganizationDetailResponse createOrganization(OrganizationCreateUpdateRequest request);
    
    OrganizationDetailResponse updateOrganization(Long id, OrganizationCreateUpdateRequest request);
    
    void deleteOrganization(Long id);
    
    OrganizationDetailResponse getOrganization(Long id);
    
    List<OrganizationDetailResponse> getAllOrganizationsDetail();
    
    List<OrganizationShortResponse> getAllOrganizationsShort();
    
    // Method to get Organization entity directly
    Organization getOrganizationEntity(Long id);
}
