package com.avinyaops.procurement.organization.department.service;

import com.avinyaops.procurement.organization.department.dto.DepartmentDTO;
import java.util.List;

public interface DepartmentService {
    DepartmentDTO createDepartment(DepartmentDTO departmentDTO);
    DepartmentDTO updateDepartment(Long id, DepartmentDTO departmentDTO);
    void deleteDepartment(Long id);
    DepartmentDTO getDepartment(Long id);
    List<DepartmentDTO> getAllDepartments();
    List<DepartmentDTO> getAllDepartmentsByOrganization(Long organizationId);
} 