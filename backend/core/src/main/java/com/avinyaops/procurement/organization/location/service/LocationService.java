package com.avinyaops.procurement.organization.location.service;

import com.avinyaops.procurement.organization.location.dto.LocationDTO;
import java.util.List;

public interface LocationService {
    LocationDTO createLocation(LocationDTO locationDTO);

    LocationDTO updateLocation(Long id, LocationDTO locationDTO);

    void deleteLocation(Long id);

    LocationDTO getLocation(Long id);

    List<LocationDTO> getAllLocations();

    List<LocationDTO> getLocationsByOrganization(Long organizationId);

    LocationDTO setPrimaryLocation(Long id);

    LocationDTO getPrimaryLocation(Long organizationId);
}