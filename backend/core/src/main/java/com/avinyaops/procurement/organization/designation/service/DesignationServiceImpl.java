package com.avinyaops.procurement.organization.designation.service;

import com.avinyaops.procurement.organization.designation.dto.DesignationDTO;
import com.avinyaops.procurement.organization.designation.model.Designation;
import com.avinyaops.procurement.organization.designation.repository.DesignationRepository;
import com.avinyaops.procurement.organization.service.OrganizationService;
import com.avinyaops.procurement.user.repository.UserRepository;
import com.avinyaops.procurement.organization.exception.ResourceInUseException;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class DesignationServiceImpl implements DesignationService {
    private final DesignationRepository designationRepository;
    private final OrganizationService organizationService;
    private final UserRepository userRepository;

    @Override
    public DesignationDTO createDesignation(DesignationDTO designationDTO) {
        // Check if designation with same name exists in the organization
        if (designationRepository.existsByNameAndOrganizationId(designationDTO.getName(), designationDTO.getOrganizationId())) {
            throw new IllegalArgumentException("Designation with name '" + designationDTO.getName() + "' already exists in the organization");
        }

        Designation designation = convertToEntity(designationDTO);
        designation = designationRepository.save(designation);
        return convertToDTO(designation);
    }

    @Override
    public DesignationDTO updateDesignation(Long id, DesignationDTO designationDTO) {
        Designation existingDesignation = designationRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Designation not found with id: " + id));

        // Check if the organization is being changed
        if (!existingDesignation.getOrganization().getId().equals(designationDTO.getOrganizationId()) && designationDTO.getOrganizationId() != null) {
            throw new IllegalArgumentException("Cannot change designation's organization");
        }

        // Check if name is being changed and if new name exists in the organization
        if (!existingDesignation.getName().equals(designationDTO.getName()) &&
                designationRepository.existsByNameAndOrganizationId(designationDTO.getName(), designationDTO.getOrganizationId())) {
            throw new IllegalArgumentException("Designation with name '" + designationDTO.getName() + "' already exists in the organization");
        }

        existingDesignation.setName(designationDTO.getName());
        existingDesignation.setDescription(designationDTO.getDescription());

        existingDesignation = designationRepository.save(existingDesignation);
        return convertToDTO(existingDesignation);
    }

    @Override
    public void deleteDesignation(Long id) {
        Designation designation = designationRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Designation not found with id: " + id));

        // Check if designation has any active employees
        if (userRepository.existsByDesignationId(id)) {
            throw new ResourceInUseException("Cannot delete designation as it has active employees associated with it");
        }

        designation.softDelete();
        designationRepository.save(designation);
    }

    @Override
    @Transactional(readOnly = true)
    public DesignationDTO getDesignation(Long id) {
        Designation designation = designationRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Designation not found with id: " + id));
        return convertToDTO(designation);
    }

    @Override
    @Transactional(readOnly = true)
    public List<DesignationDTO> getAllDesignations() {
        return designationRepository.findAll().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<DesignationDTO> getDesignationsByOrganization(Long organizationId) {
        return designationRepository.findAllByOrganizationId(organizationId).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    private DesignationDTO convertToDTO(Designation designation) {
        return DesignationDTO.builder()
                .id(designation.getId())
                .name(designation.getName())
                .description(designation.getDescription())
                .organizationId(designation.getOrganization().getId())
                .build();
    }

    private Designation convertToEntity(DesignationDTO dto) {
        return Designation.builder()
                .id(dto.getId())
                .name(dto.getName())
                .description(dto.getDescription())
                .organization(organizationService.getOrganizationEntity(dto.getOrganizationId()))
                .build();
    }
}
