package com.avinyaops.procurement.product.category.dto;

import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductCategoryRequestDTO {
    @NotBlank(message = "Category name is required")
    @Size(max = 100, message = "Category name must be less than 100 characters")
    private String name;

    @NotBlank(message = "Category description is required")
    @Size(max = 500, message = "Category description must be less than 500 characters")
    private String description;

    @NotNull(message = "Organization ID is required")
    private Long organizationId;

    private MultipartFile imageFile;
}
