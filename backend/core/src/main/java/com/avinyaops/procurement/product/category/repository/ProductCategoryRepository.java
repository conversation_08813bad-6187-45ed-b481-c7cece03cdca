package com.avinyaops.procurement.product.category.repository;

import com.avinyaops.procurement.product.category.model.ProductCategory;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProductCategoryRepository extends JpaRepository<ProductCategory, Long> {

    @Query("SELECT c FROM ProductCategory c WHERE c.organizationId = :organizationId")
    List<ProductCategory> findAllByOrganizationId(Long organizationId);

    @Query("SELECT c FROM ProductCategory c WHERE c.id = :id")
    @NonNull
    Optional<ProductCategory> findById(@NonNull Long id);

    @Query("SELECT c FROM ProductCategory c WHERE c.name = :name")
    Optional<ProductCategory> findByName(String name);

    @Query("SELECT CASE WHEN COUNT(c) > 0 THEN true ELSE false END FROM ProductCategory c " +
            "WHERE c.name = :name AND c.organizationId = :organizationId")
    boolean existsByNameAndOrganizationId(String name, Long organizationId);

    @Query("SELECT c FROM ProductCategory c WHERE c.organizationId = :organizationId OR c.organizationId IS NULL")
    List<ProductCategory> findAllByOrganizationIdOrOrganizationIdIsNull(Long organizationId);

    @Query("SELECT c FROM ProductCategory c WHERE c.organizationId IS NULL")
    List<ProductCategory> findAllByOrganizationIdIsNull();
}