package com.avinyaops.procurement.organization.controller;

import com.avinyaops.procurement.organization.dto.OrganizationCreateUpdateRequest;
import com.avinyaops.procurement.organization.dto.OrganizationDetailResponse;
import com.avinyaops.procurement.organization.dto.OrganizationShortResponse;
import com.avinyaops.procurement.organization.service.OrganizationService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/organizations")
@RequiredArgsConstructor
public class OrganizationController {
    private final OrganizationService organizationService;

    @PostMapping
    public ResponseEntity<OrganizationDetailResponse> create(@RequestBody OrganizationCreateUpdateRequest request) {
        return ResponseEntity.ok(organizationService.createOrganization(request));
    }

    @PutMapping("/{id}")
    public ResponseEntity<OrganizationDetailResponse> update(@PathVariable Long id, @RequestBody OrganizationCreateUpdateRequest request) {
        return ResponseEntity.ok(organizationService.updateOrganization(id, request));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        organizationService.deleteOrganization(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/{id}")
    public ResponseEntity<OrganizationDetailResponse> get(@PathVariable Long id) {
        return ResponseEntity.ok(organizationService.getOrganization(id));
    }

    @GetMapping
    public ResponseEntity<List<OrganizationDetailResponse>> getAll() {
        return ResponseEntity.ok(organizationService.getAllOrganizationsDetail());
    }

    @GetMapping("/short")
    public ResponseEntity<List<OrganizationShortResponse>> getAllShort() {
        return ResponseEntity.ok(organizationService.getAllOrganizationsShort());
    }
} 