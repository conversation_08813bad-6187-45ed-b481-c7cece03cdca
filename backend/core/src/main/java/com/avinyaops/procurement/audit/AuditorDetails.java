package com.avinyaops.procurement.audit;

import java.io.Serializable;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Embeddable
public class AuditorDetails implements Serializable {
    
    private static final long serialVersionUID = -743522347161302529L;
    
    @Column(name = "logged_user")
    private Long loggedUser;

    @Column(name = "client_ip_address")
    private String clientIpAddress;

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        AuditorDetails other = (AuditorDetails) obj;
        if (loggedUser == null) {
            if (other.loggedUser != null) {
                return false;
            }
        } else if (!loggedUser.equals(other.loggedUser)) {
            return false;
        }
        if (clientIpAddress == null) {
            if (other.clientIpAddress != null) {
                return false;
            }
        } else if (!clientIpAddress.equals(other.clientIpAddress)) {
            return false;
        }
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((loggedUser == null) ? 0 : loggedUser.hashCode());
        result = prime * result + ((clientIpAddress == null) ? 0 : clientIpAddress.hashCode());
        return result;
    }
}
