package com.avinyaops.procurement.product.service;

import com.avinyaops.procurement.product.dto.ProductRequestDTO;
import com.avinyaops.procurement.product.dto.ProductResponseDTO;
import java.util.List;

public interface ProductService {
    ProductResponseDTO createProduct(ProductRequestDTO productRequestDTO);
    
    ProductResponseDTO updateProduct(Long id, ProductRequestDTO productDTO);
    
    void deleteProduct(Long id);
    
    ProductResponseDTO getProduct(Long id);
    
    List<ProductResponseDTO> getAllProducts(Long organizationId);
    
    List<ProductResponseDTO> getProductsByCategory(Long categoryId, Long organizationId);
    
    List<ProductResponseDTO> getProductsBySubCategory(Long subCategoryId, Long organizationId);
    
    List<ProductResponseDTO> searchProducts(String query, Long organizationId);
} 