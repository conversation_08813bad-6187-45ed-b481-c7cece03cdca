package com.avinyaops.procurement.email.dto;

import java.util.List;
import java.util.Map;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class EmailRequest {
    private String to;
    private List<String> cc;
    private List<String> bcc;
    private String subject;
    private String templateName;
    private Map<String, Object> templateData;
    private List<EmailAttachment> attachments;
    private boolean isHtml;
    private String replyTo;
} 