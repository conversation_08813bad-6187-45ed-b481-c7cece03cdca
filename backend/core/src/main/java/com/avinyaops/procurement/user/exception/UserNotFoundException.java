package com.avinyaops.procurement.user.exception;

import com.avinyaops.procurement.exception.AvinyaException;

public class UserNotFoundException extends AvinyaException {
    private static final String ERROR_CODE = "USER_NOT_FOUND";

    public UserNotFoundException(Long id) {
        super(ERROR_CODE, String.format("User with id %d not found", id));
    }

    public UserNotFoundException(String email) {
        super(ERROR_CODE, String.format("User with email %s not found", email));
    }
} 