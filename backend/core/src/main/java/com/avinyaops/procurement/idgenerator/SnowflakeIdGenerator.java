package com.avinyaops.procurement.idgenerator;

import java.time.Instant;
import java.util.concurrent.atomic.AtomicLong;

/**
 * JavaScript-Safe Snowflake ID Generator
 *
 * Generates 47-bit unique identifiers that are guaranteed safe to use as JavaScript Numbers
 * without precision loss. Uses conservative bit allocation to ensure no precision loss.
 *
 * 47-bit Structure (Conservative JavaScript Safe):
 * |-- Timestamp (35 bits) --|-- Sequence (12 bits) --|
 *
 * - Timestamp: 35 bits (~34 years from epoch, sufficient for long-term use)
 * - Sequence: 12 bits (4096 IDs per millisecond)
 * - Total: 47 bits (well within JavaScript Number.MAX_SAFE_INTEGER)
 *
 * Original 64-bit implementation is preserved in comments for potential reversion.
 */
public class SnowflakeIdGenerator {
    // Singleton instance
    private static volatile SnowflakeIdGenerator instance;
    private static final Object lock = new Object();

    // ===== ORIGINAL 64-BIT IMPLEMENTATION (COMMENTED FOR POTENTIAL REVERSION) =====
    // // Bits allocation (64 bits total)
    // private static final long TIMESTAMP_BITS = 41;  // ~69 years of timestamps
    // private static final long SEQUENCE_BITS = 12;   // 4096 sequence numbers per millisecond
    // private static final long RESERVED_BITS = 11;   // Reserved for future use
    //
    // // Bit shifts
    // private static final long TIMESTAMP_SHIFT = SEQUENCE_BITS + RESERVED_BITS;
    // private static final long SEQUENCE_SHIFT = RESERVED_BITS;
    //
    // // Masks
    // private static final long SEQUENCE_MASK = (1L << SEQUENCE_BITS) - 1;
    // private static final long RESERVED_MASK = (1L << RESERVED_BITS) - 1;

    // ===== NEW 47-BIT IMPLEMENTATION (CONSERVATIVE JAVASCRIPT SAFE) =====
    // Bits allocation (47 bits total - Conservative JavaScript safe, no precision loss)
    private static final long TIMESTAMP_BITS = 35;  // ~34 years of timestamps (reduced for JS safety)
    private static final long SEQUENCE_BITS = 12;   // 4096 sequence numbers per millisecond (unchanged)
    // RESERVED_BITS removed to fit within JavaScript safe integer limit
    // Total: 35 + 12 = 47 bits (well within 53-bit limit, ensures no precision loss)

    // Bit shifts (updated for 47-bit structure)
    private static final long TIMESTAMP_SHIFT = SEQUENCE_BITS;  // No reserved bits
    private static final long SEQUENCE_SHIFT = 0;              // Sequence at the bottom

    // Masks (updated for 47-bit structure)
    private static final long SEQUENCE_MASK = (1L << SEQUENCE_BITS) - 1;
    private static final long TIMESTAMP_MASK = (1L << TIMESTAMP_BITS) - 1;

    // JavaScript safe integer limit (2^53 - 1)
    private static final long JAVASCRIPT_MAX_SAFE_INTEGER = 9007199254740991L; // 2^53 - 1

    // Epoch (01-01-2024 00:00:00 UTC)
    private static final long EPOCH = 1704067200000L;

    private final AtomicLong sequence = new AtomicLong(0);
    private long lastTimestamp = -1L;

    // Private constructor to prevent instantiation
    private SnowflakeIdGenerator() {
        // Initialize any required resources
    }

    /**
     * Returns the singleton instance of SnowflakeIdGenerator.
     * Uses double-checked locking for thread safety.
     *
     * @return The singleton instance
     */
    public static SnowflakeIdGenerator getInstance() {
        if (instance == null) {
            synchronized (lock) {
                if (instance == null) {
                    instance = new SnowflakeIdGenerator();
                }
            }
        }
        return instance;
    }

    public synchronized long nextId() {
        long timestamp = currentTimestamp();

        if (timestamp < lastTimestamp) {
            throw new IllegalStateException("Clock moved backwards. Refusing to generate ID.");
        }

        if (timestamp == lastTimestamp) {
            long sequenceValue = sequence.incrementAndGet() & SEQUENCE_MASK;
            if (sequenceValue == 0) {
                // Sequence overflow, wait for next millisecond
                timestamp = waitNextMillis(lastTimestamp);
            }
        } else {
            sequence.set(0);
        }

        lastTimestamp = timestamp;

        // ===== ORIGINAL 64-BIT ID GENERATION (COMMENTED) =====
        // return ((timestamp - EPOCH) << TIMESTAMP_SHIFT) |
        //        (sequence.get() << SEQUENCE_SHIFT) |
        //        (0L & RESERVED_MASK); // Reserved bits set to 0 for now

        // ===== NEW 47-BIT ID GENERATION (CONSERVATIVE JAVASCRIPT SAFE) =====
        // Structure: [35-bit timestamp][12-bit sequence] = 47 bits total
        long timestampDelta = (timestamp - EPOCH) & TIMESTAMP_MASK; // Ensure timestamp fits in 35 bits
        long id = (timestampDelta << TIMESTAMP_SHIFT) |
                  (sequence.get() & SEQUENCE_MASK);

        // Validate that the generated ID is within JavaScript safe integer limits
        if (id > JAVASCRIPT_MAX_SAFE_INTEGER) {
            throw new IllegalStateException("Generated ID exceeds JavaScript safe integer limit: " + id);
        }

        // Additional validation: ensure we're well within safe limits (47 bits max)
        long maxPossible47Bit = (1L << 47) - 1; // 2^47 - 1
        if (id > maxPossible47Bit) {
            throw new IllegalStateException("Generated ID exceeds 47-bit limit: " + id);
        }

        return id;
    }

    private long currentTimestamp() {
        return Instant.now().toEpochMilli();
    }

    private long waitNextMillis(long lastTimestamp) {
        long timestamp = currentTimestamp();
        while (timestamp <= lastTimestamp) {
            timestamp = currentTimestamp();
        }
        return timestamp;
    }

    // Utility method to extract components from an ID
    public static IdComponents extractComponents(long id) {
        // ===== ORIGINAL 64-BIT EXTRACTION (COMMENTED) =====
        // long timestamp = ((id >> TIMESTAMP_SHIFT) + EPOCH) / 1000; // Convert to seconds
        // long sequence = (id >> SEQUENCE_SHIFT) & SEQUENCE_MASK;
        // long reserved = id & RESERVED_MASK;
        // return new IdComponents(timestamp, sequence, reserved);

        // ===== NEW 47-BIT EXTRACTION (CONSERVATIVE JAVASCRIPT SAFE) =====
        // Structure: [35-bit timestamp][12-bit sequence] = 47 bits total
        long timestampDelta = (id >> TIMESTAMP_SHIFT) & TIMESTAMP_MASK;
        long timestamp = (timestampDelta + EPOCH) / 1000; // Convert to seconds
        long sequence = id & SEQUENCE_MASK; // Sequence is at the bottom (no shift needed)
        long reserved = 0L; // No reserved bits in 47-bit version

        return new IdComponents(timestamp, sequence, reserved);
    }

    // Inner class to hold ID components
    public static class IdComponents {
        private final long timestamp;
        private final long sequence;
        private final long reserved;

        public IdComponents(long timestamp, long sequence, long reserved) {
            this.timestamp = timestamp;
            this.sequence = sequence;
            this.reserved = reserved;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public long getSequence() {
            return sequence;
        }

        public long getReserved() {
            return reserved;
        }
    }
}