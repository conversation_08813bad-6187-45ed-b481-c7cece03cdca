package com.avinyaops.procurement.user.service;

import com.avinyaops.procurement.user.dto.UserCreateRequest;
import com.avinyaops.procurement.user.dto.UserResponse;
import com.avinyaops.procurement.user.dto.UserUpdateRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface UserService {
    UserResponse createUser(UserCreateRequest request);

    UserResponse updateUser(Long id, UserUpdateRequest request);

    void deleteUser(Long id);

    UserResponse getUser(Long id);

    Page<UserResponse> getAllUsers(Pageable pageable);

    UserResponse enableUser(Long id);

    UserResponse disableUser(Long id);

    Page<UserResponse> searchUsers(String searchTerm, String role, Boolean enabled, Pageable pageable);

    boolean isCurrentUser(Long userId);
}