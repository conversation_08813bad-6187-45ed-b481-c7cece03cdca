package com.avinyaops.procurement.idgenerator;

import org.hibernate.HibernateException;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.id.IdentifierGenerator;

public class AvinyaIdGenerator implements IdentifierGenerator {
    private static final SnowflakeIdGenerator idGenerator = SnowflakeIdGenerator.getInstance();

    @Override
    public Object generate(SharedSessionContractImplementor session, Object object) throws HibernateException {
        return idGenerator.nextId();
    }
} 