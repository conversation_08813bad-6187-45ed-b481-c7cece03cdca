package com.avinyaops.procurement.exception;

import java.time.Instant;
import java.util.List;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ErrorResponse {
    private Instant timestamp;
    private String errorCode;
    private String message;
    private List<String> details;
    private String path;
    private String traceId;

    public static ErrorResponse of(String errorCode, String message) {
        return ErrorResponse.builder()
                .timestamp(Instant.now())
                .errorCode(errorCode)
                .message(message)
                .build();
    }

    public static ErrorResponse of(String errorCode, String message, List<String> details) {
        return ErrorResponse.builder()
                .timestamp(Instant.now())
                .errorCode(errorCode)
                .message(message)
                .details(details)
                .build();
    }
} 