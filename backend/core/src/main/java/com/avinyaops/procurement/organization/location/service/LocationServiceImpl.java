package com.avinyaops.procurement.organization.location.service;

import com.avinyaops.procurement.organization.exception.OrganizationNotFoundException;
import com.avinyaops.procurement.organization.location.dto.AddressDTO;
import com.avinyaops.procurement.organization.location.dto.LocationDTO;
import com.avinyaops.procurement.organization.location.model.Address;
import com.avinyaops.procurement.organization.location.model.Location;
import com.avinyaops.procurement.organization.location.repository.LocationRepository;
import com.avinyaops.procurement.organization.model.Organization;
import com.avinyaops.procurement.organization.repository.OrganizationRepository;
import com.avinyaops.procurement.organization.service.OrganizationService;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class LocationServiceImpl implements LocationService {
    private final LocationRepository locationRepository;
    private final OrganizationService organizationService;
    private final OrganizationRepository organizationRepository;

    @Override
    @Transactional
    public LocationDTO createLocation(LocationDTO locationDTO) {
        // Check if location with same name exists in the organization
        if (locationRepository.existsByNameAndOrganizationId(locationDTO.getName(), locationDTO.getOrganizationId())) {
            throw new IllegalArgumentException("Location with name '" + locationDTO.getName() + "' already exists in the organization");
        }

        Location location = convertToEntity(locationDTO);
        location = locationRepository.save(location);
        return convertToDTO(location);
    }

    //TODO: add custom exceptions to this as well
    @Override
    @Transactional
    public LocationDTO updateLocation(Long id, LocationDTO locationDTO) {
        Location existingLocation = locationRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Location not found with id: " + id));

        // Check if the organization is being changed
        if (!existingLocation.getOrganization().getId().equals(locationDTO.getOrganizationId()) && locationDTO.getOrganizationId() != null) {
            throw new IllegalArgumentException("Cannot change location's organization");
        }

        // Check if name is being changed and if new name exists in the organization
        if (!existingLocation.getName().equals(locationDTO.getName()) &&
                locationRepository.existsByNameAndOrganizationId(locationDTO.getName(), locationDTO.getOrganizationId())) {
            throw new IllegalArgumentException("Location with name '" + locationDTO.getName() + "' already exists in the organization");
        }

        existingLocation.setName(locationDTO.getName());
        existingLocation.setTimezone(locationDTO.getTimezone());
        existingLocation.setAddress(mapToAddress(locationDTO.getAddress()));

        existingLocation = locationRepository.save(existingLocation);
        return convertToDTO(existingLocation);
    }

    @Override
    @Transactional
    public void deleteLocation(Long id) {
        Location location = locationRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Location not found with id: " + id));
        location.softDelete();
        locationRepository.save(location);
    }

    @Override
    @Transactional(readOnly = true)
    public LocationDTO getLocation(Long id) {
        Location location = locationRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Location not found with id: " + id));
        return convertToDTO(location);
    }

    @Override
    @Transactional(readOnly = true)
    public List<LocationDTO> getAllLocations() {
        return locationRepository.findAllActive().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<LocationDTO> getLocationsByOrganization(Long organizationId) {
        return locationRepository.findAllByOrganizationId(organizationId).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public LocationDTO setPrimaryLocation(Long id) {
        Location location = locationRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Location not found with id: " + id));

        Long orgId = location.getOrganization().getId();

        locationRepository.unsetPrimaryLocationForOrganization(orgId);

        location.setPrimary(true);
        Location savedLocation = locationRepository.save(location);

        return convertToDTO(savedLocation);
    }

    @Override
    @Transactional(readOnly = true)
    public LocationDTO getPrimaryLocation(Long organizationId) {
        if (!organizationRepository.existsById(organizationId)) {
            throw new OrganizationNotFoundException(organizationId);
        }

        Location location = locationRepository.findByOrganizationIdAndIsPrimaryTrue(organizationId)
                .orElseThrow(() -> new EntityNotFoundException(
                        "Primary location not found for organization with id: " + organizationId));

        return convertToDTO(location);
    }

    private LocationDTO convertToDTO(Location location) {
        return LocationDTO.builder()
                .id(location.getId())
                .name(location.getName())
                .timezone(location.getTimezone())
                .organizationId(location.getOrganization().getId())
                .address(mapToAddressDTO(location.getAddress()))
                .isPrimary(location.isPrimary())
                .build();
    }

    private Location convertToEntity(LocationDTO dto) {
        return Location.builder()
                .id(dto.getId())
                .name(dto.getName())
                .timezone(dto.getTimezone())
                .organization(organizationService.getOrganizationEntity(dto.getOrganizationId()))
                .address(mapToAddress(dto.getAddress()))
                .isPrimary(dto.isPrimary())
                .build();
    }

    private Address mapToAddress(AddressDTO dto) {
        if (dto == null) {
            return null;
        }
        return Address.builder()
                .addressLine1(dto.getAddressLine1())
                .addressLine2(dto.getAddressLine2())
                .city(dto.getCity())
                .state(dto.getState())
                .countryCode(dto.getCountryCode())
                .postalCode(dto.getPostalCode())
                .latitude(dto.getLatitude())
                .longitude(dto.getLongitude())
                .build();
    }

    private AddressDTO mapToAddressDTO(Address address) {
        if (address == null) {
            return null;
        }
        return AddressDTO.builder()
                .addressLine1(address.getAddressLine1())
                .addressLine2(address.getAddressLine2())
                .city(address.getCity())
                .state(address.getState())
                .countryCode(address.getCountryCode())
                .postalCode(address.getPostalCode())
                .latitude(address.getLatitude())
                .longitude(address.getLongitude())
                .build();
    }
}