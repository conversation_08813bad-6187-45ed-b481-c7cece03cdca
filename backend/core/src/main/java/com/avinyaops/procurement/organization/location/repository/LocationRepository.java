package com.avinyaops.procurement.organization.location.repository;

import com.avinyaops.procurement.organization.location.model.Location;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface LocationRepository extends JpaRepository<Location, Long> {
    @Query("SELECT l FROM Location l")
    List<Location> findAllActive();

    @Query("SELECT l FROM Location l WHERE l.id = :id")
    @NonNull
    Optional<Location> findById(@Param("id") @NonNull Long id);

    @Query("SELECT l FROM Location l WHERE l.organization.id = :organizationId")
    List<Location> findAllByOrganizationId(@Param("organizationId") Long organizationId);

    @Query("SELECT CASE WHEN COUNT(l) > 0 THEN true ELSE false END FROM Location l " +
            "WHERE l.name = :name AND l.organization.id = :organizationId")
    boolean existsByNameAndOrganizationId(@Param("name") String name, @Param("organizationId") Long organizationId);

    Optional<Location> findByOrganizationIdAndIsPrimaryTrue(Long organizationId);

    @Modifying
    @Query("UPDATE Location l SET l.isPrimary = false WHERE l.organization.id = :organizationId")
    void unsetPrimaryLocationForOrganization(@Param("organizationId") Long organizationId);

    boolean existsByIdAndOrganizationId(Long locationId, Long organizationId);

}