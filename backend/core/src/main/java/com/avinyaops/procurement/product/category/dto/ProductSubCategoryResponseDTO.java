package com.avinyaops.procurement.product.category.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSubCategoryResponseDTO {
    // ID is only used in responses, not in requests
    private Long id;

    @NotBlank(message = "Sub-category name is required")
    @Size(max = 100, message = "Sub-category name must be less than 100 characters")
    private String name;

    @NotBlank(message = "Sub-category description is required")
    @Size(max = 500, message = "Sub-category description must be less than 500 characters")
    private String description;

    @NotNull(message = "Parent category ID is required")
    private Long categoryId;

    //TODO: see if this should be optional or not
    @NotNull(message = "Organization ID is required")
    private Long organizationId;

    private String imageUrl;
} 