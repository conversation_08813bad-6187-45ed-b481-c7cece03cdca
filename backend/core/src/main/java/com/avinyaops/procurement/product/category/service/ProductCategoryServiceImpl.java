package com.avinyaops.procurement.product.category.service;

import com.avinyaops.fileupload.service.FileStorageService;
import com.avinyaops.procurement.product.category.dto.ProductCategoryRequestDTO;
import com.avinyaops.procurement.product.category.dto.ProductCategoryResponseDTO;
import com.avinyaops.procurement.product.category.dto.ProductSubCategoryRequestDTO;
import com.avinyaops.procurement.product.category.dto.ProductSubCategoryResponseDTO;
import com.avinyaops.procurement.product.category.model.ProductCategory;
import com.avinyaops.procurement.product.category.model.ProductSubCategory;
import com.avinyaops.procurement.product.category.repository.ProductCategoryRepository;
import com.avinyaops.procurement.product.category.repository.ProductSubCategoryRepository;
import com.avinyaops.procurement.product.exception.ResourceAlreadyExistsException;
import com.avinyaops.procurement.product.exception.ResourceNotFoundException;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class ProductCategoryServiceImpl implements ProductCategoryService {
    private final ProductCategoryRepository categoryRepository;
    private final ProductSubCategoryRepository subCategoryRepository;

    private final FileStorageService fileStorageService;
    private final String categoryImageFolderPath = "category";
    private final String subCategoryImageFolderPath = "subcategory";

    @Override
    @Transactional
    public ProductCategoryResponseDTO createCategory(ProductCategoryRequestDTO categoryRequestDTO) {
        // Check if category with same name exists in the organization or globally
        if (categoryRepository.existsByNameAndOrganizationId(categoryRequestDTO.getName(),
                categoryRequestDTO.getOrganizationId())) {
            throw new ResourceAlreadyExistsException("Category with this name already exists");
        }

        ProductCategory category = ProductCategory.builder()
                .name(categoryRequestDTO.getName())
                .description(categoryRequestDTO.getDescription())
                .organizationId(categoryRequestDTO.getOrganizationId())
                .imageFileId(Optional.ofNullable(categoryRequestDTO.getImageFile())
                        .map(imageFile -> fileStorageService.uploadFile(imageFile, categoryImageFolderPath).getFileId())
                        .orElse(null))
                .build();
        category = categoryRepository.save(category);
        return toCategoryResponseDTO(category);
    }

    @Override
    @Transactional
    public ProductCategoryResponseDTO updateCategory(Long id, ProductCategoryRequestDTO categoryRequestDTO) {
        ProductCategory category = categoryRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Category not found"));

        if (!category.getName().equals(categoryRequestDTO.getName()) &&
                categoryRepository.existsByNameAndOrganizationId(categoryRequestDTO.getName(),
                        categoryRequestDTO.getOrganizationId())) {
            throw new ResourceAlreadyExistsException("Category with this name already exists");
        }

        category.setName(categoryRequestDTO.getName());
        category.setDescription(categoryRequestDTO.getDescription());
        category.setOrganizationId(categoryRequestDTO.getOrganizationId());

        if (categoryRequestDTO.getImageFile() != null && !categoryRequestDTO.getImageFile().isEmpty()) {
            String newImageFileId = fileStorageService
                    .uploadFile(categoryRequestDTO.getImageFile(), categoryImageFolderPath)
                    .getFileId();
            category.setImageFileId(newImageFileId);
        } else if (category.getImageFileId() != null && !category.getImageFileId().isBlank()) {
            fileStorageService.deleteFile(category.getImageFileId());
            category.setImageFileId(null);
        }

        category = categoryRepository.save(category);
        return toCategoryResponseDTO(category);
    }

    @Override
    @Transactional
    public void deleteCategory(Long id) {
        ProductCategory category = categoryRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Category not found"));
        // TODO: cascade below and delete all related sub categories and cascade down to
        // delete all related products
        if (category.getImageFileId() != null && !category.getImageFileId().isBlank()) {
            fileStorageService.deleteFile(category.getImageFileId());
        }

        category.softDelete();
        categoryRepository.save(category);
    }

    @Override
    @Transactional(readOnly = true)
    public ProductCategoryResponseDTO getCategory(Long id) {
        ProductCategory category = categoryRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Category not found"));
        return toCategoryResponseDTO(category);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ProductCategoryResponseDTO> getAllCategories(Long organizationId) {
        if (organizationId != null) {
            // Return categories for the organization and global categories (where
            // organizationId is null)
            return categoryRepository.findAllByOrganizationIdOrOrganizationIdIsNull(organizationId).stream()
                    .map(this::toCategoryResponseDTO)
                    .collect(Collectors.toList());
        } else {
            // Return only global categories
            return categoryRepository.findAll().stream()
                    .map(this::toCategoryResponseDTO)
                    .collect(Collectors.toList());
        }
    }

    @Override
    @Transactional
    public ProductSubCategoryResponseDTO createSubCategory(Long categoryId,
            ProductSubCategoryRequestDTO subCategoryRequestDTO) {
        ProductCategory category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new ResourceNotFoundException("Category not found"));

        if (subCategoryRepository.existsByNameAndCategoryIdAndOrganizationId(
                subCategoryRequestDTO.getName(), categoryId, subCategoryRequestDTO.getOrganizationId())) {
            throw new ResourceAlreadyExistsException("Sub-category with this name already exists in the category");
        }

        ProductSubCategory subCategory = ProductSubCategory.builder()
                .name(subCategoryRequestDTO.getName())
                .description(subCategoryRequestDTO.getDescription())
                .category(category)
                .organizationId(subCategoryRequestDTO.getOrganizationId())
                .imageFileId(Optional.ofNullable(subCategoryRequestDTO.getImageFile())
                        .map(imageFile -> fileStorageService.uploadFile(imageFile, subCategoryImageFolderPath)
                                .getFileId())
                        .orElse(null))
                .build();

        subCategory = subCategoryRepository.save(subCategory);
        return toSubCategoryResponseDTO(subCategory);
    }

    @Override
    public ProductSubCategoryResponseDTO updateSubCategory(Long categoryId, Long subCategoryId,
            ProductSubCategoryRequestDTO subCategoryRequestDTO) {
        ProductSubCategory subCategory = subCategoryRepository.findById(subCategoryId)
                .orElseThrow(() -> new ResourceNotFoundException("Sub-category not found"));

        if (!subCategory.getCategory().getId().equals(categoryId)) {
            throw new ResourceNotFoundException("Sub-category does not belong to the specified category");
        }

        if (!subCategory.getName().equals(subCategoryRequestDTO.getName()) &&
                subCategoryRepository.existsByNameAndCategoryIdAndOrganizationId(
                        subCategoryRequestDTO.getName(), categoryId, subCategoryRequestDTO.getOrganizationId())) {
            throw new ResourceAlreadyExistsException("Sub-category with this name already exists in the category");
        }

        subCategory.setName(subCategoryRequestDTO.getName());
        subCategory.setDescription(subCategoryRequestDTO.getDescription());
        subCategory.setOrganizationId(subCategoryRequestDTO.getOrganizationId());

        if (subCategoryRequestDTO.getImageFile() != null && !subCategoryRequestDTO.getImageFile().isEmpty()) {
            String newImageFileId = fileStorageService
                    .uploadFile(subCategoryRequestDTO.getImageFile(), subCategoryImageFolderPath)
                    .getFileId();
            subCategory.setImageFileId(newImageFileId);
        } else if (subCategory.getImageFileId() != null && !subCategory.getImageFileId().isBlank()) {
            fileStorageService.deleteFile(subCategory.getImageFileId());
            subCategory.setImageFileId(null);
        }

        subCategory = subCategoryRepository.save(subCategory);
        return toSubCategoryResponseDTO(subCategory);
    }

    @Override
    public void deleteSubCategory(Long categoryId, Long subCategoryId) {
        ProductSubCategory subCategory = subCategoryRepository.findById(subCategoryId)
                .orElseThrow(() -> new ResourceNotFoundException("Sub-category not found"));

        if (!subCategory.getCategory().getId().equals(categoryId)) {
            throw new ResourceNotFoundException("Sub-category does not belong to the specified category");
        }

        if (subCategory.getImageFileId() != null && !subCategory.getImageFileId().isBlank()) {
            fileStorageService.deleteFile(subCategory.getImageFileId());
        }

        subCategory.softDelete();
        subCategoryRepository.save(subCategory);
    }

    @Override
    public ProductSubCategoryResponseDTO getSubCategory(Long categoryId, Long subCategoryId) {
        ProductSubCategory subCategory = subCategoryRepository.findById(subCategoryId)
                .orElseThrow(() -> new ResourceNotFoundException("Sub-category not found"));

        if (!subCategory.getCategory().getId().equals(categoryId)) {
            throw new ResourceNotFoundException("Sub-category does not belong to the specified category");
        }

        return toSubCategoryResponseDTO(subCategory);
    }

    @Override
    public List<ProductSubCategoryResponseDTO> getAllSubCategoriesByCategoryId(Long categoryId) {
        return subCategoryRepository.findAllByCategoryId(
                categoryId).stream()
                .map(this::toSubCategoryResponseDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ProductSubCategoryResponseDTO> getAllSubCategories(Long organizationId) {
        return subCategoryRepository.findAllByOrganizationId(
                organizationId).stream()
                .map(this::toSubCategoryResponseDTO)
                .collect(Collectors.toList());

    }

    private ProductCategoryResponseDTO toCategoryResponseDTO(ProductCategory category) {
        return ProductCategoryResponseDTO.builder()
                .id(category.getId())
                .name(category.getName())
                .description(category.getDescription())
                .organizationId(category.getOrganizationId())
                .imageUrl(
                        Optional.ofNullable(category.getImageFileId())
                                .filter(fileId -> !fileId.isBlank())
                                .map(fileStorageService::generateViewUrl)
                                .orElse(null))
                .build();
    }

    private ProductSubCategoryResponseDTO toSubCategoryResponseDTO(ProductSubCategory subCategory) {
        return ProductSubCategoryResponseDTO.builder()
                .id(subCategory.getId())
                .name(subCategory.getName())
                .description(subCategory.getDescription())
                .categoryId(subCategory.getCategory().getId())
                .organizationId(subCategory.getOrganizationId())
                .imageUrl(
                        Optional.ofNullable(subCategory.getImageFileId())
                                .filter(fileId -> !fileId.isBlank())
                                .map(fileStorageService::generateViewUrl)
                                .orElse(null))
                .build();
    }
}