package com.avinyaops.procurement.organization.dto;

import com.avinyaops.procurement.organization.location.dto.LocationDTO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrganizationDetailResponse {
    private Long id;
    private String name;
    private String description;
    private boolean enabled;
    private String logoFileId;
    private LocationDTO primaryLocation;
}