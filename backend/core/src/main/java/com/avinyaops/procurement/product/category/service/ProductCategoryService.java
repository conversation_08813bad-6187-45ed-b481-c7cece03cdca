package com.avinyaops.procurement.product.category.service;

import com.avinyaops.procurement.product.category.dto.ProductCategoryRequestDTO;
import com.avinyaops.procurement.product.category.dto.ProductCategoryResponseDTO;
import com.avinyaops.procurement.product.category.dto.ProductSubCategoryRequestDTO;
import com.avinyaops.procurement.product.category.dto.ProductSubCategoryResponseDTO;

import java.util.List;

public interface ProductCategoryService {
    ProductCategoryResponseDTO createCategory(ProductCategoryRequestDTO categoryRequestDTO);
    
    ProductCategoryResponseDTO updateCategory(Long id, ProductCategoryRequestDTO categoryRequestDTO);
    
    void deleteCategory(Long id);
    
    ProductCategoryResponseDTO getCategory(Long id);
    
    List<ProductCategoryResponseDTO> getAllCategories(Long organizationId);
    
    ProductSubCategoryResponseDTO createSubCategory(Long categoryId, ProductSubCategoryRequestDTO subCategoryDTO);
    
    ProductSubCategoryResponseDTO updateSubCategory(Long categoryId, Long subCategoryId, ProductSubCategoryRequestDTO subCategoryDTO);
    
    void deleteSubCategory(Long categoryId, Long subCategoryId);
    
    ProductSubCategoryResponseDTO getSubCategory(Long categoryId, Long subCategoryId);
    
    List<ProductSubCategoryResponseDTO> getAllSubCategoriesByCategoryId(Long categoryId);
    
    List<ProductSubCategoryResponseDTO> getAllSubCategories(Long organizationId);
} 