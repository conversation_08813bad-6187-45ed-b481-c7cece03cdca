package com.avinyaops.procurement.exception;

import lombok.Getter;

@Getter
public class AvinyaException extends RuntimeException {
    private final String errorCode;
    private final String message;
    private final Object[] args;

    public AvinyaException(String errorCode, String message) {
        this(errorCode, message, (Object[]) null);
    }

    public AvinyaException(String errorCode, String message, Object... args) {
        super(message);
        this.errorCode = errorCode;
        this.message = message;
        this.args = args;
    }

    public AvinyaException(String errorCode, String message, Throwable cause) {
        this(errorCode, message, cause, (Object[]) null);
    }

    public AvinyaException(String errorCode, String message, Throwable cause, Object... args) {
        super(message, cause);
        this.errorCode = errorCode;
        this.message = message;
        this.args = args;
    }
} 