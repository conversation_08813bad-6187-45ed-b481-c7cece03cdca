package com.avinyaops.procurement.common.phoneNumber;

import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber.PhoneNumber;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class PhoneNumberValidator implements ConstraintValidator<ValidPhoneNumber, String> {
    private final PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.getInstance();
    private String defaultRegion;
    private boolean requireCountryCode;

    @Override
    public void initialize(ValidPhoneNumber constraintAnnotation) {
        this.defaultRegion = constraintAnnotation.defaultRegion();
        this.requireCountryCode = constraintAnnotation.requireCountryCode();
    }

    @Override
    public boolean isValid(String phoneNumber, ConstraintValidatorContext context) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return true; // Let @NotNull or @NotBlank handle null/empty validation
        }

        try {
            PhoneNumber parsedNumber;
            if (requireCountryCode) {
                parsedNumber = phoneNumberUtil.parse(phoneNumber, null);

                if (parsedNumber.getCountryCode() == 0) {
                    return false;
                }
            } else {
                parsedNumber = phoneNumberUtil.parse(phoneNumber, defaultRegion);
            }

            if (!phoneNumberUtil.isValidNumber(parsedNumber)) {
                return false;
            }
            return true;
        } catch (NumberParseException nex) {
            return false;
        }
    }
}
