package com.avinyaops.procurement.idgenerator;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

class SnowflakeIdGeneratorTest {

    private SnowflakeIdGenerator idGenerator;

    @BeforeEach
    void setUp() {
        idGenerator = SnowflakeIdGenerator.getInstance();
    }

    @Test
    void testSingletonInstance() {
        SnowflakeIdGenerator instance1 = SnowflakeIdGenerator.getInstance();
        SnowflakeIdGenerator instance2 = SnowflakeIdGenerator.getInstance();
        assertSame(instance1, instance2, "Both instances should be the same");
    }

    @Test
    void testNextId() {
        long id = idGenerator.nextId();
        assertTrue(id > 0);
    }

    @Test
    void testNextIdUniqueness() {
        Set<Long> ids = new HashSet<>();
        for (int i = 0; i < 1000; i++) {
            long id = idGenerator.nextId();
            assertTrue(ids.add(id), "Generated duplicate ID: " + id);
        }
    }

    @Test
    void testNextIdConcurrent() throws InterruptedException {
        int threadCount = 10;
        int idsPerThread = 1000;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        Set<Long> ids = new HashSet<>();

        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < idsPerThread; j++) {
                        synchronized (ids) {
                            long id = idGenerator.nextId();
                            assertTrue(ids.add(id), "Generated duplicate ID: " + id);
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        assertTrue(latch.await(5, TimeUnit.SECONDS), "Test timed out");
        assertEquals(threadCount * idsPerThread, ids.size(), "Expected " + (threadCount * idsPerThread) + " unique IDs");
        executor.shutdown();
    }

    @Test
    void testNextIdMonotonic() {
        long previousId = 0;
        for (int i = 0; i < 1000; i++) {
            long currentId = idGenerator.nextId();
            assertTrue(currentId > previousId, "Generated non-monotonic ID sequence");
            previousId = currentId;
        }
    }

    @Test
    void testExtractComponents() {
        long id = idGenerator.nextId();
        SnowflakeIdGenerator.IdComponents components = SnowflakeIdGenerator.extractComponents(id);

        assertTrue(components.getTimestamp() > 0, "Timestamp should be positive");
        assertTrue(components.getSequence() >= 0 && components.getSequence() < 4096,
            "Sequence should be between 0 and 4095");
        assertEquals(0L, components.getReserved(), "Reserved bits should be 0");
    }

    @Test
    void testConcurrentSingletonAccess() throws InterruptedException {
        int threadCount = 10;
        CountDownLatch latch = new CountDownLatch(threadCount);
        Set<SnowflakeIdGenerator> instances = new HashSet<>();

        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    SnowflakeIdGenerator instance = SnowflakeIdGenerator.getInstance();
                    synchronized (instances) {
                        instances.add(instance);
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        assertTrue(latch.await(5, TimeUnit.SECONDS), "Test timed out");
        assertEquals(1, instances.size(), "All threads should get the same instance");
        executor.shutdown();
    }

    @Test
    void testJavaScriptSafety() {
        // Test that generated IDs are within JavaScript safe integer limits
        for (int i = 0; i < 1000; i++) {
            long id = idGenerator.nextId();

            // Check that ID is within JavaScript Number.MAX_SAFE_INTEGER
            assertTrue(id <= 9007199254740991L, "ID exceeds JavaScript safe integer limit: " + id);

            // Check that ID is within our 47-bit limit
            long maxPossible47Bit = (1L << 47) - 1;
            assertTrue(id <= maxPossible47Bit, "ID exceeds 47-bit limit: " + id);

            // Simulate JavaScript precision loss test
            // In JavaScript, numbers lose precision when they exceed certain thresholds
            // This test ensures our IDs won't lose precision
            String idStr = Long.toString(id);
            long parsedBack = Long.parseLong(idStr);
            assertEquals(id, parsedBack, "ID lost precision when converted to/from string: " + id);
        }
    }

    @Test
    void testBitStructure() {
        long id = idGenerator.nextId();

        // Extract components manually to verify bit structure
        long sequence = id & ((1L << 12) - 1); // Last 12 bits
        long timestamp = (id >> 12) & ((1L << 35) - 1); // Next 35 bits

        // Verify sequence is within expected range
        assertTrue(sequence >= 0 && sequence < 4096, "Sequence out of range: " + sequence);

        // Verify timestamp is reasonable (should be positive and not too large)
        assertTrue(timestamp > 0, "Timestamp should be positive: " + timestamp);
        assertTrue(timestamp < (1L << 35), "Timestamp exceeds 35-bit limit: " + timestamp);

        // Verify total ID uses only 47 bits
        assertTrue(id < (1L << 47), "ID exceeds 47-bit limit: " + id);
    }
}