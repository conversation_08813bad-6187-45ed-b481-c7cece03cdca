package com.avinyaops.procurement.fileupload.exception;

import com.avinyaops.procurement.exception.AvinyaException;

public class FileNotFoundException extends AvinyaException {
    private static final String ERROR_CODE = "FILE_NOT_FOUND";

    public FileNotFoundException(String fileId) {
        super(ERROR_CODE, String.format("File not found with ID: %s", fileId));
    }

    public FileNotFoundException(String fileId, Throwable cause) {
        super(ERROR_CODE, String.format("File not found with ID: %s", fileId), cause);
    }
}