package com.avinyaops.procurement.fileupload.util;

import com.avinyaops.procurement.fileupload.exception.InvalidFileTypeException;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class FileTypeValidator {
    private static final Set<String> ALLOWED_IMAGE_TYPES = new HashSet<>(Arrays.asList(
            "image/jpeg", "image/png", "image/gif", "image/webp"));

    private static final Set<String> ALLOWED_DOCUMENT_TYPES = new HashSet<>(Arrays.asList(
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));

    public static void validateImage(MultipartFile file) {
        if (!ALLOWED_IMAGE_TYPES.contains(file.getContentType())) {
            throw new InvalidFileTypeException(file.getOriginalFilename(),
                    String.join(", ", ALLOWED_IMAGE_TYPES));
        }
    }

    public static void validateDocument(MultipartFile file) {
        if (!ALLOWED_DOCUMENT_TYPES.contains(file.getContentType())) {
            throw new InvalidFileTypeException(file.getOriginalFilename(),
                    String.join(", ", ALLOWED_DOCUMENT_TYPES));
        }
    }

    public static void validateFileType(MultipartFile file) {
        if (!(ALLOWED_DOCUMENT_TYPES.contains(file.getContentType())
                || ALLOWED_IMAGE_TYPES.contains(file.getContentType()))) {
            throw new InvalidFileTypeException(file.getOriginalFilename(),
                    String.join(", ", ALLOWED_DOCUMENT_TYPES) + " or " + String.join(", ", ALLOWED_IMAGE_TYPES));
        }
    }

    public static void validateFileType(MultipartFile file, Set<String> allowedTypes) {
        if (!allowedTypes.contains(file.getContentType())) {
            throw new InvalidFileTypeException(file.getOriginalFilename(),
                    String.join(", ", allowedTypes));
        }
    }
}