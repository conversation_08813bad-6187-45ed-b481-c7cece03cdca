Avinya Ops
Description: A full-stack application with React frontend and Spring Boot backend

Rules:

1. Project Structure
   Description: The project is divided into two main directories: app (frontend) and api (backend)
   Details:
   - app/ - React frontend with TypeScript and Vite
   - api/ - Spring Boot backend with Java 17

2. Development Guidelines
   Description: General development rules for the project
   Details:
   - Use TypeScript for frontend development
   - Follow Java 17 coding standards for backend
   - Maintain consistent code formatting using <PERSON><PERSON><PERSON> (frontend)
   - Use meaningful commit messages
   - Keep documentation updated

3. Branching Strategy
   Description: Git branching and workflow rules
   Details:
   - main - Production-ready code
   - dev - Development branch
   - feature/* - New features
   - bugfix/* - Bug fixes
   - hotfix/* - Urgent production fixes