import { Outlet } from '@tanstack/react-router';
import { useSidebar } from '@/hooks/useSidebar';
import { AppHeader } from '@/components/layout/AppHeader';
import SidebarV2 from '@/components/ui/SidebarV2';

export const MainLayout: React.FC = () => {
  const { isOpen } = useSidebar(); // Use context state and toggle function

  return (
    <div className="flex flex-column min-h-screen">
      <div className="flex flex-1">
        <SidebarV2 />
        {/* Adjust main content class based on sidebar state */}
        <main className={`flex-1 p-4 main-content ${isOpen ? 'sidebar-pushed' : ''}`}>
          <div className="flex align-items-center">
            <AppHeader />
          </div>
          <div>
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
};