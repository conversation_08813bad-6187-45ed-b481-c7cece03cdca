/* ==========================================================================
   Typography
   ========================================================================== */
:root {
    /* Font Family */
    --main-font: "Poppins", sans-serif;

    /* Font Sizes */
    --text-xs: 0.75rem;    /* 12px */
    --text-sm: 0.875rem;   /* 14px */
    --text-base: 1rem;     /* 16px */
    --text-lg: 1.125rem;   /* 18px */
    --text-xl: 1.25rem;    /* 20px */
    --text-2xl: 1.5rem;    /* 24px */
    --text-3xl: 1.875rem;  /* 30px */
    --text-4xl: 2.25rem;   /* 36px */
    --text-5xl: 3rem;      /* 48px */
    --text-6xl: 3.75rem;   /* 60px */
    --text-7xl: 4.5rem;    /* 72px */
    --text-7-5xl: 5.25rem; /* 84px */
    --text-8xl: 6rem;      /* 96px */
    --text-9xl: 8rem;      /* 128px */
    --text-10xl: 10rem;    /* 160px */

    /* Font Weights */
    --font-thin: 100;
    --font-extralight: 200;
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;
    --font-black: 900;
}

/* ==========================================================================
   Brand Colors
   ========================================================================== */
:root {
    /* Primary Colors */
    --color-primary: #00CBA9;
    --color-primary-light: #4CFFD8;
    --color-primary-dark: #00806A;

    /* Secondary Colors */
    --color-secondary: #ff6200;
    --color-secondary-light: #ff8133;
    --color-secondary-dark: #cc4e00;

    /* Danger Colors */
    --color-danger: #ff0000;
    --color-danger-light: #ff2e2e;
    --color-danger-dark: #ca291a;
}

/* ==========================================================================
   UI Colors
   ========================================================================== */
:root {
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f5f7fa;
    --bg-tertiary: #e9ecef;
    --bg-quaternary: #dee2e6;

    /* Text Colors */
    --text-theme: var(--color-primary);
    --text-primary: #000000;
    --text-secondary: #4a4a4a;
    --text-tertiary: #666666;
    --text-quaternary: #999999;
    --text-on-primary: #ffffff;
    --text-on-secondary: #ffffff;
    --text-on-danger: #ffffff;
    --text-muted: #6c757d;

    /* Border Colors */
    --border-primary: #dee2e6;
    --border-secondary: #ced4da;
    --border-focus: var(--color-primary);
    --border-error: var(--color-error);
}

/* ==========================================================================
   Component Colors
   ========================================================================== */
:root {
    /* Button Colors */
    --btn-primary-bg: var(--color-primary);
    --btn-primary-bg-hover: var(--color-primary-dark);
    --btn-secondary-bg: var(--color-secondary);
    --btn-secondary-bg-hover: var(--color-secondary-dark);
    --btn-danger-bg: var(--color-danger);
    --btn-danger-bg-hover: var(--color-danger-dark);
    --btn-outline-bg: transparent;
    --btn-outline-bg-hover: var(--bg-tertiary);
}

/* ==========================================================================
   Status Colors
   ========================================================================== */
:root {
    /* Success Colors */
    --color-success: #28a745;
    --color-success-light: #34ce57;
    --color-success-dark: #218838;

    /* Warning Colors */
    --color-warning: #ffc107;
    --color-warning-light: #ffca2c;
    --color-warning-dark: #d39e00;

    /* Error Colors */
    --color-error: #dc3545;
    --color-error-light: #e4606d;
    --color-error-dark: #bd2130;

    /* Info Colors */
    --color-info: #17a2b8;
    --color-info-light: #1ec8e0;
    --color-info-dark: #138496;
}

/* ==========================================================================
   Effects
   ========================================================================== */
:root {
    /* Shadow Colors */
    --shadow-color: rgba(0, 0, 0, 0.1);
    --shadow-color-dark: rgba(0, 0, 0, 0.2);
    --card-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);

    /* Border Radius */
    --border-radius-sm: 0.25rem;
    --border-radius: 0.375rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 0.75rem;
    --border-radius-2xl: 1rem;
    --border-radius-full: 9999px;

    /* Layout Variables */
    --sidebar-background-color: #ffffff;
    --sidebar-width: 250px;
    --sidebar-width-collapsed: 60px;
    --header-height: 60px;

    /* Page Heading Variables */
    --page-heading-font-size: var(--text-2xl);
    --page-heading-font-weight: var(--font-semibold);
    --page-heading-color: var(--text-primary);
    --page-heading-margin-bottom: 1.5rem;

    /* Card Padding Variables */
    --card-padding: 1rem;
    --card-padding-mobile: 1rem;
    --card-padding-screen: 0;
    --card-header-padding: 1.25rem 1.25rem 0.75rem;
    --card-header-padding-mobile: 1rem 1rem 0.5rem;
}