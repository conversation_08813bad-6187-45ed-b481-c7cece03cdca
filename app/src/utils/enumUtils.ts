/**
 * Utility functions for working with enums
 */

/**
 * Get all values from a TypeScript enum
 * @param enumObject The enum object
 * @returns Array of enum values
 */
export function getEnumValues<T extends Record<string, string | number>>(enumObject: T): string[] {
  // Filter out numeric keys that TypeScript adds to string enums
  return Object.keys(enumObject)
    .filter(key => isNaN(Number(key)))
    .map(key => enumObject[key] as string);
}

/**
 * Get all keys from a TypeScript enum
 * @param enumObject The enum object
 * @returns Array of enum keys
 */
export function getEnumKeys<T extends Record<string, string | number>>(enumObject: T): string[] {
  // Filter out numeric keys that TypeScript adds to string enums
  return Object.keys(enumObject).filter(key => isNaN(Number(key)));
}
