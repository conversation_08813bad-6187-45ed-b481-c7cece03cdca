/**
 * Utility functions for date formatting and manipulation
 */

/**
 * Format a date string to a localized date string
 * @param dateString ISO date string or any valid date input
 * @returns Formatted date string
 */
export const formatDate = (dateString: string): string => {
  if (!dateString) return '-';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString; // Return the original string if parsing fails
  }
};

/**
 * Format a date string to a localized date and time string
 * @param dateString ISO date string or any valid date input
 * @returns Formatted date and time string
 */
export const formatDateTime = (dateString: string): string => {
  if (!dateString) return '-';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleString();
  } catch (error) {
    console.error('Error formatting date and time:', error);
    return dateString; // Return the original string if parsing fails
  }
};

/**
 * Format a date value to a string in YYYY-MM-DD format
 * @param dateValue Date object or string
 * @returns Formatted date string in YYYY-MM-DD format
 */
export const formatDateValue = (dateValue: Date | string | null | undefined): string => {
  if (!dateValue) return '';

  if (dateValue instanceof Date) {
    const year = dateValue.getFullYear();
    const month = String(dateValue.getMonth() + 1).padStart(2, '0');
    const day = String(dateValue.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  if (typeof dateValue === 'string') {
    // If it's already a string, try to parse and format it
    try {
      const date = new Date(dateValue);
      if (!isNaN(date.getTime())) {
        return formatDateValue(date);
      }
      // If parsing fails, return the original string
      return dateValue;
    } catch {
      return dateValue;
    }
  }

  // Fallback: convert to string
  return String(dateValue);
};
