/**
 * Custom hooks for object storage operations
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ObjectStorageService } from '@/services/api/objectStorageService';
import { 
  FileUploadOptions, 
  GetFileUrlRequest 
} from '@/types/storage.types';

/**
 * Hook for uploading a single file
 * @returns Mutation for file upload with loading, error, and success states
 */
export const useUploadFile = () => {
  return useMutation({
    mutationFn: ({ file, options }: { file: File, options?: FileUploadOptions }) => 
      ObjectStorageService.uploadFile(file, options),
  });
};

/**
 * Hook for uploading multiple files
 * @returns Mutation for batch file upload with loading, error, and success states
 */
export const useUploadFiles = () => {
  return useMutation({
    mutationFn: ({ files, options }: { files: File[], options?: FileUploadOptions }) => 
      ObjectStorageService.uploadFiles(files, options),
  });
};

/**
 * Hook for getting a presigned URL for a file
 * @param request The request containing the file key and optional expiration time
 * @param enabled Whether to enable the query (default: true)
 * @returns Query result with the presigned URL data, loading state, and error state
 */
export const useFileUrl = (request: GetFileUrlRequest, enabled = true) => {
  return useQuery({
    queryKey: ['fileUrl', request],
    queryFn: () => ObjectStorageService.getFileUrl(request),
    enabled: enabled && !!request.key,
  });
};

/**
 * Hook for deleting a file
 * @returns Mutation for file deletion with loading, error, and success states
 */
export const useDeleteFile = () => {
  return useMutation({
    mutationFn: ({ key, bucket }: { key: string, bucket?: string }) => 
      ObjectStorageService.deleteFile(key, bucket),
  });
};
