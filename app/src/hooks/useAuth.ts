import { useState, useEffect } from 'react';
import { useNavigate } from '@tanstack/react-router'

interface User {
  id: string;
  username: string;
  email: string;
  role: string;
}

interface AuthContext {
  user: User | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
}

// Dummy user data
const DUMMY_USER: User = {
  id: '1',
  username: 'admin',
  email: '<EMAIL>',
  role: 'admin'
};

export const useAuth = (): AuthContext => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is logged in (from localStorage)
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      setUser(JSON.parse(storedUser));
      setIsAuthenticated(true);
    }
  }, []);

  const login = async (email: string, password: string) => {
    // Dummy authentication logic
    if (email === '<EMAIL>' && password === 'admin') {
      setUser(DUMMY_USER);
      setIsAuthenticated(true);
      localStorage.setItem('user', JSON.stringify(DUMMY_USER));
      navigate({ to: '/' });
    } else {
      throw new Error('Invalid credentials');
    }
  };

  const logout = () => {
    setUser(null);
    setIsAuthenticated(false);
    localStorage.removeItem('user');
    navigate({ to: '/login' });
  };

  return {
    user,
    //TODO: Change this later
    isAuthenticated: true,
    login,
    logout
  };
}; 