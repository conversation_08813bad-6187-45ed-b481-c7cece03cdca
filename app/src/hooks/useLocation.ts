import { useState } from 'react';
import { LocationService } from '@/services/api/locationService';
import { Location, CreateLocationRequest, UpdateLocationRequest } from '@/types/location.types';
import { ApiResponse } from '@/types/api/common';

/**
 * Hook for creating a new location
 * Currently using mock data service - will be updated when real API is integrated
 */
export const useCreateLocation = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createLocation = async (data: CreateLocationRequest): Promise<ApiResponse<Location> | null> => {
    setLoading(true);
    setError(null);
    try {
      // Using the mock service implementation
      const response = await LocationService.createLocation(data);
      return response;
    } catch (err) {
      setError('Failed to create location');
      console.error('Error creating location:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { createLocation, loading, error };
};

/**
 * Hook for updating an existing location
 * Currently using mock data service - will be updated when real API is integrated
 */
export const useUpdateLocation = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateLocation = async (id: string, data: UpdateLocationRequest): Promise<ApiResponse<Location> | null> => {
    setLoading(true);
    setError(null);
    try {
      // Using the mock service implementation
      const response = await LocationService.updateLocation(id, data);
      return response;
    } catch (err) {
      setError('Failed to update location');
      console.error('Error updating location:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { updateLocation, loading, error };
};

/**
 * Hook for deleting a location
 * Currently using mock data service - will be updated when real API is integrated
 */
export const useDeleteLocation = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const deleteLocation = async (id: string): Promise<boolean> => {
    setLoading(true);
    setError(null);
    try {
      // Using the mock service implementation
      await LocationService.deleteLocation(id);
      return true;
    } catch (err) {
      setError('Failed to delete location');
      console.error('Error deleting location:', err);
      return false;
    } finally {
      setLoading(false);
    }
  };

  return { deleteLocation, loading, error };
};

/**
 * Hook for setting a location as primary
 * Currently using mock data service - will be updated when real API is integrated
 */
export const useSetPrimaryLocation = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const setPrimaryLocation = async (id: string): Promise<ApiResponse<Location> | null> => {
    setLoading(true);
    setError(null);
    try {
      // Using the mock service implementation
      const response = await LocationService.setPrimaryLocation(id);
      return response;
    } catch (err) {
      setError('Failed to set primary location');
      console.error('Error setting primary location:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { setPrimaryLocation, loading, error };
};
