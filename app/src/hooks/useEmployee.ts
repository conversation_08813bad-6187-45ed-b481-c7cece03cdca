/**
 * Custom hooks for employee management operations
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { EmployeeService } from '@/services/api/employeeService';
import {
  CreateEmployeeRequest,
  UpdateEmployeeRequest,
  EmployeeStatus
} from '@/types/employee';
import { QueryParams } from '@/types/api/common';

/**
 * Hook for fetching a paginated list of employees
 * @param params Query parameters for pagination, sorting, and filtering
 * @returns Query result with employees data, loading state, and error state
 */
export const useEmployees = (params?: QueryParams) => {
  return useQuery({
    queryKey: ['employees', params],
    queryFn: () => EmployeeService.getEmployees(params),
  });
};

/**
 * Hook for fetching employees by department ID
 * @param departmentId Department ID
 * @param params Additional query parameters
 * @returns Query result with employees data, loading state, and error state
 */
export const useEmployeesByDepartment = (departmentId: string, params?: QueryParams) => {
  const queryParams = {
    ...params,
    filter: {
      ...params?.filter,
      department_id: departmentId
    }
  };

  return useQuery({
    queryKey: ['employees', 'department', departmentId, params],
    queryFn: () => EmployeeService.getEmployees(queryParams),
    enabled: !!departmentId, // Only run the query if a department ID is provided
  });
};

/**
 * Hook for fetching a single employee by ID
 * @param id Employee ID
 * @returns Query result with employee data, loading state, and error state
 */
export const useEmployee = (id: string) => {
  return useQuery({
    queryKey: ['employee', id],
    queryFn: () => EmployeeService.getEmployeeById(id),
    enabled: !!id, // Only run the query if an ID is provided
  });
};

/**
 * Hook for creating a new employee
 * @returns Mutation function and states for creating an employee
 */
export const useCreateEmployee = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateEmployeeRequest) => EmployeeService.createEmployee(data),
    onSuccess: () => {
      // Invalidate the employees list query to trigger a refetch
      queryClient.invalidateQueries({ queryKey: ['employees'] });
    },
  });
};

/**
 * Hook for updating an existing employee
 * @param id Employee ID
 * @returns Mutation function and states for updating an employee
 */
export const useUpdateEmployee = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateEmployeeRequest) => EmployeeService.updateEmployee(id, data),
    onSuccess: () => {
      // Invalidate both the employees list and the specific employee queries
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      queryClient.invalidateQueries({ queryKey: ['employee', id] });
    },
  });
};

/**
 * Hook for deleting an employee
 * @returns Mutation function and states for deleting an employee
 */
export const useDeleteEmployee = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => EmployeeService.deleteEmployee(id),
    onSuccess: () => {
      // Invalidate the employees list query to trigger a refetch
      queryClient.invalidateQueries({ queryKey: ['employees'] });
    },
  });
};

/**
 * Hook for updating an employee's status
 * @param id Employee ID
 * @returns Mutation function and states for updating an employee's status
 */
export const useUpdateEmployeeStatus = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (status: EmployeeStatus) =>
      EmployeeService.updateEmployee(id, { status }),
    onSuccess: () => {
      // Invalidate both the employees list and the specific employee queries
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      queryClient.invalidateQueries({ queryKey: ['employee', id] });
    },
  });
};

/**
 * Hook for terminating an employee
 * @param id Employee ID
 * @returns Mutation function and states for terminating an employee
 */
export const useTerminateEmployee = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (terminationDate: string) =>
      EmployeeService.updateEmployee(id, {
        status: EmployeeStatus.TERMINATED,
        termination_date: terminationDate
      }),
    onSuccess: () => {
      // Invalidate both the employees list and the specific employee queries
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      queryClient.invalidateQueries({ queryKey: ['employee', id] });
    },
  });
};
