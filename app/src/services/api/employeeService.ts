/**
 * Service for handling employee-related API operations
 */

import { apiClient } from './client';
import {
  Employee,
  CreateEmployeeRequest,
  UpdateEmployeeRequest,
  EmployeeResponse,
} from '@/types/employee';
import { ApiResponse, PaginatedResponse, QueryParams } from '@/types/api/common';

/**
 * Service class for employee management operations
 */
export class EmployeeService {
  private static BASE_URL = '/employees';

  /**
   * Get a paginated list of employees
   * @param params Query parameters for pagination, sorting, and filtering
   * @returns Promise with paginated employee data
   */
  static async getEmployees(params?: QueryParams): Promise<PaginatedResponse<Employee>> {
    try {
      return await apiClient.get<PaginatedResponse<Employee>>(this.BASE_URL, { params });
    } catch (error) {
      console.error('Error fetching employees:', error);
      throw error;
    }
  }

  /**
   * Get a single employee by ID
   * @param id Employee ID
   * @returns Promise with employee data
   */
  static async getEmployeeById(id: string): Promise<ApiResponse<Employee>> {
    try {
      return await apiClient.get<ApiResponse<Employee>>(`${this.BASE_URL}/${id}`);
    } catch (error) {
      console.error(`Error fetching employee with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new employee
   * @param data Employee data
   * @returns Promise with created employee data
   */
  static async createEmployee(data: CreateEmployeeRequest): Promise<ApiResponse<Employee>> {
    try {
      // Format the data to match the database schema
      const formattedData = {
        ...data,
        // Convert joining_date to the format expected by the backend if provided
        joining_date: data.joining_date || new Date().toISOString().split('T')[0],
        // Set default organization_id if not provided
        organization_id: data.organization_id || '1'
      };

      return await apiClient.post<ApiResponse<Employee>>(this.BASE_URL, formattedData);
    } catch (error) {
      console.error('Error creating employee:', error);
      throw error;
    }
  }

  /**
   * Update an existing employee
   * @param id Employee ID
   * @param data Updated employee data
   * @returns Promise with updated employee data
   */
  static async updateEmployee(id: string, data: UpdateEmployeeRequest): Promise<ApiResponse<Employee>> {
    try {
      // Format the data to match the database schema
      const formattedData = {
        ...data,
        // Convert joining_date to the format expected by the backend if provided
        joining_date: data.joining_date ? data.joining_date : undefined,
        // Format termination_date if provided
        termination_date: data.termination_date ? data.termination_date : undefined
      };

      return await apiClient.put<ApiResponse<Employee>>(`${this.BASE_URL}/${id}`, formattedData);
    } catch (error) {
      console.error(`Error updating employee with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete an employee
   * @param id Employee ID
   * @returns Promise with deletion confirmation
   */
  static async deleteEmployee(id: string): Promise<ApiResponse<void>> {
    try {
      return await apiClient.delete<ApiResponse<void>>(`${this.BASE_URL}/${id}`);
    } catch (error) {
      console.error(`Error deleting employee with ID ${id}:`, error);
      throw error;
    }
  }
}
