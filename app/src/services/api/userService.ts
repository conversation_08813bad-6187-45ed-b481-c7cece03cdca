import { apiClient } from './client';
import {
  User,
  CreateUserRequest,
  UpdateUserRequest,
  LoginRequest,
  LoginResponse,
} from '@/types/api/user';
import { ApiResponse, PaginatedResponse, QueryParams } from '@/types/api/common';

export class UserService {
  private static BASE_URL = '/users';

  static async getUsers(params?: QueryParams): Promise<PaginatedResponse<User>> {
    return apiClient.get<PaginatedResponse<User>>(this.BASE_URL, { params });
  }

  static async getUserById(id: string): Promise<ApiResponse<User>> {
    return apiClient.get<ApiResponse<User>>(`${this.BASE_URL}/${id}`);
  }

  static async createUser(data: CreateUserRequest): Promise<ApiResponse<User>> {
    return apiClient.post<ApiResponse<User>>(this.BASE_URL, data);
  }

  static async updateUser(id: string, data: UpdateUserRequest): Promise<ApiResponse<User>> {
    return apiClient.put<ApiResponse<User>>(`${this.BASE_URL}/${id}`, data);
  }

  static async deleteUser(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<ApiResponse<void>>(`${this.BASE_URL}/${id}`);
  }

  static async login(data: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    return apiClient.post<ApiResponse<LoginResponse>>('/auth/login', data);
  }

  static async logout(): Promise<void> {
    localStorage.removeItem('auth_token');
  }
}