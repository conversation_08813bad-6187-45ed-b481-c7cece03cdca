/**
 * Service for handling object storage operations
 */

import { apiClient } from './client';
import {
  FileUploadResponse,
  BatchFileUploadResponse,
  GetFileUrlRequest,
  GetFileUrlResponse,
  FileUploadOptions
} from '@/types/storage.types';
import { ApiResponse } from '@/types/api/common';

/**
 * Service class for object storage operations
 */
export class ObjectStorageService {
  private static BASE_URL = '/storage';

  /**
   * Upload a single file to the object storage
   * @param file The file to upload
   * @param options Upload options (bucket, folder, etc.)
   * @returns Promise with the uploaded file data
   */
  static async uploadFile(file: File, options?: FileUploadOptions): Promise<ApiResponse<FileUploadResponse>> {
    try {
      // For development/demo, simulate file upload with a delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Generate a mock response
      const folder = options?.folder || 'default';
      const fileName = file.name.replace(/[^a-zA-Z0-9.]/g, '_');
      const key = `${folder}/${Date.now()}_${fileName}`;

      // Use a placeholder image URL based on the file type
      let imageUrl = 'https://via.placeholder.com/150';

      // If it's an image file, use a random unsplash image
      if (file.type.startsWith('image/')) {
        const imageId = Math.floor(Math.random() * 1000);
        imageUrl = `https://source.unsplash.com/random/300x300?sig=${imageId}`;
      }

      return {
        data: {
          key,
          filename: file.name,
          size: file.size,
          contentType: file.type,
          url: imageUrl,
        },
        message: 'File uploaded successfully',
        status: 200,
      };

      // Production implementation - commented out for now
      /*
      const formData = new FormData();
      formData.append('file', file);

      // Add options to the form data if provided
      if (options) {
        if (options.bucket) formData.append('bucket', options.bucket);
        if (options.folder) formData.append('folder', options.folder);
        if (options.public !== undefined) formData.append('public', options.public.toString());

        // Add metadata if provided
        if (options.metadata) {
          Object.entries(options.metadata).forEach(([key, value]) => {
            formData.append(`metadata[${key}]`, value);
          });
        }
      }

      // Use multipart/form-data content type (handled by FormData)
      return await apiClient.post<ApiResponse<FileUploadResponse>>(
        `${this.BASE_URL}/upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      */
    } catch (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
  }

  /**
   * Upload multiple files to the object storage
   * @param files Array of files to upload
   * @param options Upload options (bucket, folder, etc.)
   * @returns Promise with the batch upload response
   */
  static async uploadFiles(files: File[], options?: FileUploadOptions): Promise<ApiResponse<BatchFileUploadResponse>> {
    try {
      const formData = new FormData();

      // Append each file to the form data
      files.forEach((file) => {
        formData.append(`files`, file);
      });

      // Add options to the form data if provided
      if (options) {
        if (options.bucket) formData.append('bucket', options.bucket);
        if (options.folder) formData.append('folder', options.folder);
        if (options.public !== undefined) formData.append('public', options.public.toString());

        // Add metadata if provided
        if (options.metadata) {
          Object.entries(options.metadata).forEach(([key, value]) => {
            formData.append(`metadata[${key}]`, value);
          });
        }
      }

      // Use multipart/form-data content type (handled by FormData)
      return await apiClient.post<ApiResponse<BatchFileUploadResponse>>(
        `${this.BASE_URL}/upload/batch`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
    } catch (error) {
      console.error('Error uploading files:', error);
      throw error;
    }
  }

  /**
   * Get a presigned URL for a file in the object storage
   * @param request The request containing the file key and optional expiration time
   * @returns Promise with the presigned URL data
   */
  static async getFileUrl(request: GetFileUrlRequest): Promise<ApiResponse<GetFileUrlResponse>> {
    try {
      return await apiClient.post<ApiResponse<GetFileUrlResponse>>(
        `${this.BASE_URL}/url`,
        request
      );
    } catch (error) {
      console.error('Error getting file URL:', error);
      throw error;
    }
  }

  /**
   * Delete a file from the object storage
   * @param key The key/path of the file to delete
   * @param bucket Optional bucket name (if not using default)
   * @returns Promise with the deletion response
   */
  static async deleteFile(key: string, bucket?: string): Promise<ApiResponse<void>> {
    try {
      // For development/demo, simulate file deletion with a delay
      await new Promise(resolve => setTimeout(resolve, 500));

      return {
        data: undefined,
        message: 'File deleted successfully',
        status: 200,
      };

      // Production implementation - commented out for now
      /*
      const params: Record<string, string> = { key };
      if (bucket) params.bucket = bucket;

      return await apiClient.delete<ApiResponse<void>>(`${this.BASE_URL}`, { params });
      */
    } catch (error) {
      console.error('Error deleting file:', error);
      throw error;
    }
  }
}
