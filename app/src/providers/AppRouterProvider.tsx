import { createHashHist<PERSON>, createRouter, RouterProvider } from '@tanstack/react-router';
import { routeTree } from '@/routes';
import AuthAwareNotFound from '@/pages/common/AuthAwareNotFound';

// Create the hash history and router once (outside the component to avoid re-creation)
const hashHistory = createHashHistory();
const router = createRouter({ routeTree, history: hashHistory, defaultNotFoundComponent: AuthAwareNotFound });

// Extend TanStack Router types (same as in original code)
declare module '@tanstack/react-router' {
    interface Register {
        router: typeof router;
    }
    interface HistoryState {
        from?: { pathname: string };
    }
}

// Reusable AppRouterProvider component
const AppRouterProvider: React.FC = (props) => {
    return <RouterProvider router={router} {...props} />;
};

export default AppRouterProvider;