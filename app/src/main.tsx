import React from 'react'
import ReactDOM from 'react-dom/client'
import { QueryProvider } from '@/providers/QueryProvider'
import { PrimeReactProvider } from 'primereact/api'
import { SidebarProvider } from '@/context/SidebarContext'
import { App } from './App'
import '@/styles/index.css'
import '@/styles/prime-theme.css'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <PrimeReactProvider>
      <QueryProvider>
        <SidebarProvider>
          <App />
        </SidebarProvider>
      </QueryProvider>
    </PrimeReactProvider>
  </React.StrictMode>,
)