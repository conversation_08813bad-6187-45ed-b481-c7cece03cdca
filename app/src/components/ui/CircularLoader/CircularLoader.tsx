import { ProgressSpinner } from 'primereact/progressspinner';
import './CircularLoader.css';

interface CircularLoaderProps {
    size?: 'small' | 'medium' | 'large';
    strokeWidth?: string;
    className?: string;
}

const CircularLoader = ({
    size = 'medium',
    strokeWidth = '4',
    className = '',
}: CircularLoaderProps) => {
    const getSizeClass = () => {
        switch (size) {
            case 'small':
                return 'circular-loader--small';
            case 'medium':
                return 'circular-loader--medium';
            case 'large':
                return 'circular-loader--large';
            default:
                return '';
        }
    };

    const baseClass = 'circular-loader';
    const sizeClass = getSizeClass();
    const loaderClasses = `${baseClass} ${sizeClass} ${className}`;

    return (
        <div className={loaderClasses}>
            <ProgressSpinner 
                strokeWidth={strokeWidth}
                style={{ width: '100%', height: '100%' }}
            />
        </div>
    );
};

export default CircularLoader; 