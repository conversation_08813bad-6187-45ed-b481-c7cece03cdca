@import url("../../../variables.css");

/* Main sidebar container */
.sidebar-v2 {
  width: 280px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
  border-right: 1px solid var(--border-primary);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.05);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), 
              opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1), 
              visibility 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Sidebar states */
.sidebar-v2.open {
  transform: translateX(0);
  opacity: 1;
  visibility: visible;
}

.sidebar-v2.closed {
  transform: translateX(-100%);
  opacity: 0;
  visibility: hidden;
}

/* Header section */
.sidebar-header {
  padding: 1rem;
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0; /* Prevent header from shrinking */
}

.sidebar-title {
  color: var(--color-primary);
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  margin: 0;
}

/* Close button in header */
.sidebar-close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: var(--border-radius-full);
  border: none;
  background-color: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.sidebar-close-btn:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.sidebar-close-btn i {
  font-size: 0.9rem;
}

/* Menu section */
.sidebar-menu {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 0;
}

/* Styling scrollbar */
.sidebar-menu::-webkit-scrollbar {
  width: 6px;
}

.sidebar-menu::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-menu::-webkit-scrollbar-thumb {
  background-color: var(--bg-quaternary);
  border-radius: var(--border-radius-full);
}

/* Menu items */
.sidebar-menu-item {
  padding-bottom: 0.25rem;
}

.sidebar-menu-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.sidebar-menu-link:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.sidebar-menu-link.active {
  background-color: var(--bg-secondary);
  color: var(--color-primary);
  border-left: 3px solid var(--color-primary);
}

.sidebar-menu-link i {
  font-size: 1.2rem;
  margin-right: 0.75rem;
  width: 20px;
  text-align: center;
}

.sidebar-menu-text {
  flex: 1;
  font-size: var(--text-sm);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Accordion icon styling */
.sidebar-accordion-icon {
  font-size: 0.8rem;
  margin-left: auto;
  margin-right: 0;
  transition: transform 0.3s ease;
}

.chevron-collapsed {
  transform: rotate(0deg);
}

.chevron-expanded {
  transform: rotate(90deg);
}

/* Submenu items */
.sidebar-submenu {
  padding-left: 1.6rem;
}

.sidebar-submenu .sidebar-menu-link {
  padding: 0.5rem 1rem;
}

.sidebar-submenu .sidebar-menu-link i {
  font-size: 1rem;
}

i.sidebar-accordion-icon {
  font-size: 0.8rem !important;
}

.sidebar-submenu .sidebar-menu-item {
  border-left: 0.5px solid var(--color-primary);
}

/* Profile section at bottom */
.sidebar-profile {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-top: 1px solid var(--border-primary);
  background-color: var(--bg-primary);
  flex-shrink: 0;
  position: relative;
}

.sidebar-profile-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--color-primary);
  color: var(--text-on-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  margin-right: 0.75rem;
}

.sidebar-profile-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-profile-name {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar-profile-role {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

.sidebar-profile-more {
  background: transparent;
  border: none;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-secondary);
  transition: background-color 0.2s ease;
}

.sidebar-profile-more:hover {
  background-color: var(--bg-secondary);
}

.sidebar-profile-more i {
  font-size: 1rem;
}

/* Profile popover menu */
.sidebar-profile-popover {
  position: absolute;
  bottom: 100%;
  right: 1rem;
  width: 150px;
  background-color: var(--bg-primary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.sidebar-profile-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-profile-menu-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  cursor: pointer;
  color: var(--text-secondary);
  transition: background-color 0.2s ease;
}

.sidebar-profile-menu-item:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.sidebar-profile-menu-item i {
  margin-right: 0.75rem;
  font-size: 1rem;
}

/* Responsive styles */
@media (max-width: 768px) {
  .sidebar-v2.closed {
    transform: translateX(-100%);
  }
  
  .sidebar-v2.open {
    transform: translateX(0);
  }
  
  /* Mobile overlay */
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 1;
    transition: opacity 0.3s ease;
  }
}

/* Main content push effect */
.main-content {
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-left: 0;
}

.main-content.sidebar-pushed {
  margin-left: 280px;
}

/* Ensure icon container is consistent */
.sidebar-menu-link i.sidebar-accordion-icon {
  margin-left: auto;
  margin-right: 0;
} 