@import url(../../../variables.css);

.breadcrumb {
    background: transparent;
    border: none;
    padding: 0;
}

.breadcrumb .p-breadcrumb {
    background: transparent;
    border: none;
    padding: 0;
}

.breadcrumb .p-breadcrumb ul li .p-menuitem-link {
    color: var(--text-secondary);
    transition: color 0.2s;
}

.breadcrumb .p-breadcrumb ul li .p-menuitem-link:hover {
    color: var(--text-primary);
}

.breadcrumb .p-breadcrumb ul li .p-menuitem-link .p-menuitem-icon {
    color: var(--text-secondary);
}

.breadcrumb .p-breadcrumb ul li .p-menuitem-link .p-menuitem-text {
    color: var(--text-secondary);
}

.breadcrumb .p-breadcrumb ul li.p-breadcrumb-chevron {
    color: var(--text-tertiary);
    margin: 0 0.5rem;
}

.breadcrumb .p-breadcrumb ul li:last-child .p-menuitem-link {
    color: var(--text-primary);
    font-weight: var(--font-medium);
}

.breadcrumb-wrapper {
  padding: 0.5rem 1rem;
}

.custom-breadcrumb {
  --breadcrumb-padding: 0.5rem;
  --breadcrumb-font-size: 0.875rem;
  background: transparent;
  border: none;
}

.breadcrumb-menuitem {
  font-size: var(--breadcrumb-font-size);
  color: var(--text-color-secondary);
  transition: color 0.2s ease;
  padding: 0.25rem 0;
}

.breadcrumb-menuitem .p-menuitem-text {
  color: var(--text-theme);
}

.breadcrumb-menuitem:hover {
  color: var(--color-primary);
}

.home-breadcrumb {
  color: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.home-breadcrumb i {
  color: var(--color-primary);
  transition: color 0.2s ease;
  font-size: 1.1rem;
}

.home-breadcrumb:hover i {
  color: var(--color-primary-dark, var(--color-primary));
}

.breadcrumb-separator {
  color: var(--text-color-secondary);
  opacity: 0.4;
  margin: 0 0.5rem;
}

/* Responsive styles */
@media screen and (max-width: 768px) {
  .custom-breadcrumb {
    --breadcrumb-padding: 0.25rem;
    --breadcrumb-font-size: 0.75rem;
  }
  
  .breadcrumb-wrapper {
    padding: 0.25rem 0;
  }
} 