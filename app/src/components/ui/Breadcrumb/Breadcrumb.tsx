import React, { useMemo } from 'react';
import { useRouter, useMatches } from '@tanstack/react-router';
import { BreadCrumb } from 'primereact/breadcrumb';
import { MenuItem } from 'primereact/menuitem';
import { useResponsive } from '@/hooks/useResponsive';
import './Breadcrumb.css';

interface BreadcrumbProps {
  className?: string;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ className = '' }) => {
  const router = useRouter();
  const matches = useMatches();
  const { isMobile } = useResponsive();

  const breadcrumbItems = useMemo(() => {
    // Initialize items with home icon
    const items: MenuItem[] = [
      {
        icon: 'pi pi-home',
        command: () => router.navigate({ to: '/' }),
        className: 'home-breadcrumb',
        template: (item) => (
          <i className={item.icon} />
        ),
      },
    ];

    // Get all matched routes
    const matchedRoutes = [...matches];

    // Filter out the root and app routes
    const filteredRoutes = matchedRoutes.filter(match =>
      match.routeId !== '__root__' &&
      match.routeId !== '/app'
    );

    // Sort routes by pathname length to ensure correct order
    filteredRoutes.sort((a, b) => a.pathname.length - b.pathname.length);

    // Process each route to build the breadcrumb
    filteredRoutes.forEach(match => {
      // Get the path segments
      const pathSegments = match.pathname.split('/').filter(Boolean);

      // Skip if this is just the app segment
      if (pathSegments.length === 1 && pathSegments[0] === 'app') {
        return;
      }

      // Get the last segment for the label (the current route's segment)
      const lastSegment = pathSegments[pathSegments.length - 1];

      // Format the label
      const label = lastSegment
        .replace(/_/g, ' ')
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');

      // Add the segment to breadcrumb items
      items.push({
        label: isMobile && label.length > 15 ? `${label.substring(0, 15)}...` : label,
        command: () => router.navigate({ to: match.pathname }),
        className: 'breadcrumb-item',
      });
    });

    return items;
  }, [matches, router, isMobile]);

  return (
    <div className={`breadcrumb-wrapper ${className}`}>
      <BreadCrumb
        model={breadcrumbItems}
        className="custom-breadcrumb"
        pt={{
          root: { className: 'p-0' },
          menuitem: { className: 'breadcrumb-menuitem' },
          separator: { className: 'breadcrumb-separator' },
        }}
      />
    </div>
  );
};

export default Breadcrumb;