@import url(../../../variables.css);

.button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    border-radius: 0.375rem;
    font-family: var(--main-font);
    font-weight: var(--font-medium);
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    border: none;
    outline: none;
}

/* Variants */
.button--primary {
    background-color: var(--btn-primary-bg);
    color: var(--text-on-primary);
}

.button--primary:hover:not(:disabled) {
    background-color: var(--btn-primary-bg-hover);
}

.button--secondary {
    background-color: var(--btn-secondary-bg);
    color: var(--text-on-secondary);
}

.button--secondary:hover:not(:disabled) {
    background-color: var(--btn-secondary-bg-hover);
}

.button--danger {
    background-color: var(--btn-danger-bg);
    color: var(--text-on-danger);
}

.button--danger:hover:not(:disabled) {
    background-color: var(--btn-danger-bg-hover);
}

.button--outline {
    background-color: var(--btn-outline-bg);
    color: var(--color-primary);
}

.button--outline:hover:not(:disabled) {
    background-color: var(--btn-outline-bg-hover);
    color: var(--text-on-primary);
}

/* Sizes */
.button--small {
    font-size: var(--text-sm);
    padding: 0.375rem 0.75rem;
}

.button--medium {
    font-size: var(--text-base);
    padding: 0.5rem 1rem;
}

.button--large {
    font-size: var(--text-lg);
    padding: 0.75rem 1.5rem;
}

/* States */
.button--full-width {
    width: 100%;
}

.button--loading {
    opacity: 0.7;
    cursor: not-allowed;
}

.button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Spinner */
.button__spinner {
    margin-right: 0.5rem;
} 