import React from 'react';
import './Card.css';

interface CardProps {
    children: React.ReactNode;
    title?: string;
    subtitle?: string;
    className?: string;
    header?: React.ReactNode;
    footer?: React.ReactNode;
    variant?: 'default' | 'elevated' | 'outlined' | 'flat';
    padding?: 'none' | 'small' | 'medium' | 'large';
    hoverable?: boolean;
    onClick?: () => void;
}

const Card = ({
    children,
    title,
    subtitle,
    className = '',
    header,
    footer,
    variant = 'default',
    padding = 'medium',
    hoverable = false,
    onClick,
}: CardProps) => {
    const baseClass = 'card';
    const variantClass = `card--${variant}`;
    const paddingClass = `card--padding-${padding}`;
    const hoverableClass = hoverable ? 'card--hoverable' : '';
    const clickableClass = onClick ? 'card--clickable' : '';
    
    const classes = [
        baseClass,
        variantClass,
        paddingClass,
        hoverableClass,
        clickableClass,
        className
    ].filter(Boolean).join(' ');

    return (
        <div className={classes} onClick={onClick}>
            {(header || title) && (
                <div className="card__header">
                    {header || (
                        <>
                            {title && <h3 className="card__title">{title}</h3>}
                            {subtitle && <p className="card__subtitle">{subtitle}</p>}
                        </>
                    )}
                </div>
            )}
            <div className="card__content">
                {children}
            </div>
            {footer && (
                <div className="card__footer">
                    {footer}
                </div>
            )}
        </div>
    );
};

export default Card; 