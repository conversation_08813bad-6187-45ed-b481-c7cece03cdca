import React from 'react';
import { Dialog } from 'primereact/dialog';
import Button, { ButtonVariant, ButtonSize } from '@components/ui/Button/Button';
import './Modal.css';

export interface FooterButton {
    label: string;
    onClick: () => void;
    className?: string;
    icon?: string;
    variant?: ButtonVariant;
    size?: ButtonSize;
    isLoading?: boolean;
    leftIcon?: React.ReactNode;
    rightIcon?: React.ReactNode;
    fullWidth?: boolean;
    disabled?: boolean;
}

interface ModalProps {
    visible: boolean;
    onHide: () => void;
    header?: string | JSX.Element;
    children: React.ReactNode;
    footerButtons?: FooterButton[];
    modalProps?: Omit<React.ComponentProps<typeof Dialog>, 'visible' | 'onHide' | 'header' | 'footer'>;
}

const Modal: React.FC<ModalProps> = ({
    visible,
    onHide,
    header,
    children,
    footerButtons = [],
    modalProps = {},
}) => {
    const renderFooter = () => {
        return (
            <div className="flex justify-content-end gap-2">
                {footerButtons.map((btn, idx) => (
                    <Button
                        key={idx}
                        icon={btn.icon}
                        onClick={btn.onClick}
                        className={btn.className}
                        variant={btn.variant}
                        size={btn.size}
                        isLoading={btn.isLoading}
                        leftIcon={btn.leftIcon}
                        rightIcon={btn.rightIcon}
                        fullWidth={btn.fullWidth}
                        disabled={btn.disabled}
                    >
                        {btn.label}
                    </Button>
                ))}
            </div>
        );
    };

    return (
        <Dialog
            draggable={false}
            className="dialog"
            visible={visible}
            onHide={onHide}
            header={header}
            footer={footerButtons.length > 0 ? renderFooter() : null}
            modal
            {...modalProps}
        >
            {children}
        </Dialog>
    );
};

export default Modal;
