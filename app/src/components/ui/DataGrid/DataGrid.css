.data-grid-container {
    width: 100%;
}

.data-grid-container .p-datatable {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.data-grid-container .p-datatable .p-datatable-header {
    background: var(--surface-section);
    border: 1px solid var(--surface-border);
    border-bottom: 0;
    padding: 1rem;
}

.data-grid-container .p-datatable .p-datatable-thead > tr > th {
    background: var(--surface-section);
    color: var(--text-color);
    font-weight: 600;
    padding: 1rem;
    border: 1px solid var(--surface-border);
    transition: background-color 0.2s;
}

.data-grid-container .p-datatable .p-datatable-tbody > tr > td {
    padding: 1rem;
    border: 1px solid var(--surface-border);
    transition: background-color 0.2s;
}

.data-grid-container .p-datatable .p-datatable-tbody > tr:hover {
    background: var(--surface-hover);
}

.data-grid-container .p-datatable .p-datatable-tbody > tr.p-highlight {
    background: var(--highlight-bg);
    color: var(--highlight-text-color);
}

/* Pagination styles */
.data-grid-container .p-paginator {
    background: var(--surface-section);
    border: 1px solid var(--surface-border);
    border-top: 0;
    padding: 0.5rem;
}

.data-grid-container .p-paginator .p-paginator-pages .p-paginator-page {
    min-width: 2.5rem;
    height: 2.5rem;
    margin: 0.2rem;
    border-radius: var(--border-radius);
    transition: background-color 0.2s;
}

.data-grid-container .p-paginator .p-paginator-pages .p-paginator-page.p-highlight {
    background: var(--primary-color);
    color: var(--primary-color-text);
}

/* Loading state */
.data-grid-container .p-datatable-loading-overlay {
    background: rgba(255, 255, 255, 0.7);
}

/* Empty state */
.data-grid-container .p-datatable-emptymessage {
    padding: 2rem;
    text-align: center;
    color: var(--text-color-secondary);
}

/* Sort icon styles */
.data-grid-container .p-datatable .p-datatable-thead > tr > th .p-sortable-column-icon {
    color: var(--text-color-secondary) !important;
    margin-left: 0.5rem;
}

.data-grid-container .p-datatable .p-datatable-thead > tr > th.p-highlight .p-sortable-column-icon {
    color: var(--text-color-secondary) !important;
}

.data-grid-container .p-datatable .p-datatable-thead > tr > th .p-sortable-column-badge {
    display: none;
}

/* Override PrimeReact's default purple/primary color for sorted columns */
.data-grid-container .p-datatable .p-datatable-thead > tr > th.p-highlight {
    background: var(--surface-section) !important;
    color: var(--text-color) !important;
}

/* Ensure sort icons are always grey regardless of sort state */
.data-grid-container .p-datatable .p-datatable-thead > tr > th .p-icon {
    color: var(--text-color-secondary) !important;
}

/* Responsive styles */
@media screen and (max-width: 768px) {
    .data-grid-container .p-datatable .p-datatable-thead > tr > th,
    .data-grid-container .p-datatable .p-datatable-tbody > tr > td {
        padding: 0.75rem;
    }

    .data-grid-container .p-paginator {
        padding: 0.25rem;
    }

    .data-grid-container .p-paginator .p-paginator-pages .p-paginator-page {
        min-width: 2rem;
        height: 2rem;
    }
}

.data-grid-container .data-grid-paginator .p-paginator-page {
    background-color: var(--color-primary) !important;
}