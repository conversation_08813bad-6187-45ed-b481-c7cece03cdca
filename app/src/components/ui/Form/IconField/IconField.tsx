import { InputText } from 'primereact/inputtext';
import './IconField.css';
import { KeyFilterType } from 'primereact/keyfilter';

export interface IconFieldProps {
  icon?: string;
  iconPosition?: 'left' | 'right';
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  required?: boolean;
  name?: string;
  type?: string;
  error?: boolean;
  errorMessage?: string;
  id?: string;
  keyfilter?: KeyFilterType;
  min?: number;
  max?: number;
  step?: number;
  // Add props from register to support react-hook-form
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  ref?: React.Ref<HTMLInputElement>;
  value?: string;
}

const IconField = ({
  icon,
  iconPosition = 'left',
  placeholder,
  className = '',
  disabled = false,
  required = false,
  name,
  type = 'text',
  error = false,
  errorMessage,
  id,
  keyfilter,
  min,
  max,
  step,
  onChange,
  onBlur,
  ref,
  value,
}: IconFieldProps) => {
  const baseClass = 'icon-field';
  const positionClass = `icon-field--${iconPosition}`;
  const errorClass = error ? 'icon-field--error' : '';
  const classes = `${baseClass} ${positionClass} ${errorClass} ${className}`;

  return (
    <div className={classes}>
      {icon && <i className={`pi ${icon} icon-field__icon`} />}
      <InputText
        id={id}
        ref={ref} // Pass ref from register
        onChange={onChange} // Pass onChange from register
        onBlur={onBlur} // Pass onBlur from register
        placeholder={placeholder}
        disabled={disabled}
        required={required}
        name={name}
        type={type}
        keyfilter={keyfilter}
        min={min}
        max={max}
        step={step}
        value={value}
        className="icon-field__input"
      />
      {error && errorMessage && (
        <small className="icon-field__error">{errorMessage}</small>
      )}
    </div>
  );
};

export default IconField;