.icon-field {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
}

.icon-field__icon {
    position: absolute;
    color: var(--text-tertiary);
    z-index: 1;
}

.icon-field--left .icon-field__icon {
    left: 0.75rem;
}

.icon-field--right .icon-field__icon {
    right: 0.75rem;
}

.icon-field__input {
    width: 100%;
    padding: 0.75rem;
    padding-left: 2.5rem;
    border: 1px solid var(--border-primary);
    border-radius: 0.375rem;
    font-family: var(--main-font);
    font-size: var(--text-base);
    color: var(--text-primary);
    transition: all 0.2s ease-in-out;
}

.icon-field--right .icon-field__input {
    padding-left: 0.75rem;
    padding-right: 2.5rem;
}

.icon-field__input:hover:not(:disabled) {
    border-color: var(--border-focus);
}

.icon-field__input:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 2px var(--color-primary-light);
}

.icon-field__input:disabled {
    background-color: var(--bg-tertiary);
    cursor: not-allowed;
}

.icon-field--error .icon-field__input {
    border-color: var(--color-error);
}

.icon-field--error .icon-field__input:focus {
    box-shadow: 0 0 0 2px var(--color-error-light);
}

.icon-field__error {
    position: absolute;
    bottom: -1.25rem;
    left: 0;
    color: var(--color-error);
    font-size: var(--text-xs);
} 