@import url(../../../../variables.css);

.password-field {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
}

.password-field__input {
    width: 100%;
    display: block;
}

.password-field__icon {
    position: absolute;
    color: var(--text-tertiary);
    z-index: 1;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
}

.password-field__input .p-password-input {
    width: 100%;
    padding-left: 2.5rem;
}

.password-field--error .p-password-input {
    border-color: var(--color-error) !important;
}

.password-field--error .p-password-input:focus {
    box-shadow: 0 0 0 2px var(--color-error-light) !important;
}

.password-field__error {
    color: var(--color-error);
    font-size: var(--text-xs);
    margin-top: 0.25rem;
}
