import { Password } from 'primereact/password';
import './PasswordField.css';

export interface PasswordFieldProps {
    icon?: string;
    value?: string;
    onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
    placeholder?: string;
    className?: string;
    disabled?: boolean;
    required?: boolean;
    name?: string;
    error?: boolean;
    errorMessage?: string;
    toggleMask?: boolean;
    feedback?: boolean;
    promptLabel?: string;
    weakLabel?: string;
    mediumLabel?: string;
    strongLabel?: string;
    id?: string;
}

const PasswordField = ({
    icon,
    value,
    onChange,
    placeholder,
    className = '',
    disabled = false,
    required = false,
    name,
    error = false,
    errorMessage,
    toggleMask = true,
    feedback = true,
    promptLabel = 'Enter a password',
    weakLabel = 'Too simple',
    mediumLabel = 'Average complexity',
    strongLabel = 'Strong password',
    id,
}: PasswordFieldProps) => {
    const errorClass = error ? 'password-field--error' : '';
    const classes = `password-field ${errorClass} ${className}`;

    return (
        <div className={classes}>
            <div className="relative">
                {icon && <i className={`pi ${icon} password-field__icon`} />}
                <Password
                    id={id}
                    value={value}
                    onChange={onChange}
                    placeholder={placeholder}
                    disabled={disabled}
                    required={required}
                    name={name}
                    toggleMask={toggleMask}
                    feedback={feedback}
                    promptLabel={promptLabel}
                    weakLabel={weakLabel}
                    mediumLabel={mediumLabel}
                    strongLabel={strongLabel}
                    className={`password-field__input ${error ? 'p-invalid' : ''}`}
                    inputClassName="w-full"
                />
            </div>
            {error && errorMessage && (
                <small className="password-field__error">{errorMessage}</small>
            )}
        </div>
    );
};

export default PasswordField;