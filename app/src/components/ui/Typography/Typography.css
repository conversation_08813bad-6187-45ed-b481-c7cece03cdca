@import url(../../../variables.css);

.typography {
    font-family: var(--main-font);
    margin: 0;
    line-height: 1.5;
}

/* Variants */
.typography--h1 {
    font-size: var(--text-7-5xl);
    line-height: 1.2;
}

.typography--h2 {
    font-size: var(--text-6xl);
    line-height: 1.2;
}

.typography--h3 {
    font-size: var(--text-5xl);
    line-height: 1.2;
}

.typography--h4 {
    font-size: var(--text-4xl);
    line-height: 1.3;
}

.typography--h5 {
    font-size: var(--text-3xl);
    line-height: 1.3;
}

.typography--h6 {
    font-size: var(--text-2xl);
    line-height: 1.4;
}

.typography--subtitle1 {
    font-size: var(--text-xl);
    line-height: 1.4;
}

.typography--subtitle2 {
    font-size: var(--text-lg);
    line-height: 1.4;
}

.typography--body1 {
    font-size: var(--text-base);
    line-height: 1.5;
}

.typography--body2 {
    font-size: var(--text-sm);
    line-height: 1.5;
}

.typography--caption {
    font-size: var(--text-xs);
    line-height: 1.5;
}

.typography--overline {
    font-size: var(--text-xs);
    line-height: 1.5;
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

/* Colors */
.typography--theme {
    color: var(--text-theme);
}

.typography--primary {
    color: var(--text-primary);
}

.typography--secondary {
    color: var(--text-secondary);
}

.typography--tertiary {
    color: var(--text-tertiary);
}

.typography--quaternary {
    color: var(--text-quaternary);
}

.typography--muted {
    color: var(--text-muted);
}

/* Font Weights */
.typography--thin {
    font-weight: var(--font-thin);
}

.typography--extralight {
    font-weight: var(--font-extralight);
}

.typography--light {
    font-weight: var(--font-light);
}

.typography--normal {
    font-weight: var(--font-normal);
}

.typography--medium {
    font-weight: var(--font-medium);
}

.typography--semibold {
    font-weight: var(--font-semibold);
}

.typography--bold {
    font-weight: var(--font-bold);
}

.typography--extrabold {
    font-weight: var(--font-extrabold);
}

.typography--black {
    font-weight: var(--font-black);
}

/* Text Alignment */
.typography--left {
    text-align: left;
}

.typography--center {
    text-align: center;
}

.typography--right {
    text-align: right;
} 