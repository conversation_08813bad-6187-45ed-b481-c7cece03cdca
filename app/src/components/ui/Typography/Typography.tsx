import { HTMLAttributes } from 'react';
import './Typography.css';

export type TypographyVariant = 
    | 'h1' 
    | 'h2' 
    | 'h3' 
    | 'h4' 
    | 'h5' 
    | 'h6' 
    | 'subtitle1' 
    | 'subtitle2' 
    | 'body1' 
    | 'body2' 
    | 'caption' 
    | 'overline';

export type TypographyColor = 
    | 'theme' 
    | 'primary' 
    | 'secondary' 
    | 'tertiary' 
    | 'quaternary' 
    | 'muted';

interface TypographyProps extends HTMLAttributes<HTMLElement> {
    variant?: TypographyVariant;
    color?: TypographyColor;
    weight?: 'thin' | 'extralight' | 'light' | 'normal' | 'medium' | 'semibold' | 'bold' | 'extrabold' | 'black';
    align?: 'left' | 'center' | 'right';
    className?: string;
    children: React.ReactNode;
}

const Typography = ({
    variant = 'body1',
    color = 'primary',
    weight = 'normal',
    align = 'left',
    className = '',
    children,
    ...props
}: TypographyProps) => {
    const getComponent = () => {
        switch (variant) {
            case 'h1':
            case 'h2':
            case 'h3':
            case 'h4':
            case 'h5':
            case 'h6':
                return variant;
            case 'subtitle1':
            case 'subtitle2':
                return 'h6';
            case 'body1':
            case 'body2':
                return 'p';
            case 'caption':
            case 'overline':
                return 'span';
            default:
                return 'p';
        }
    };

    const Component = getComponent();
    const baseClass = 'typography';
    const variantClass = `typography--${variant}`;
    const colorClass = `typography--${color}`;
    const weightClass = `typography--${weight}`;
    const alignClass = `typography--${align}`;

    const typographyClasses = `${baseClass} ${variantClass} ${colorClass} ${weightClass} ${alignClass} ${className}`;

    return (
        <Component className={typographyClasses} {...props}>
            {children}
        </Component>
    );
};

export default Typography; 