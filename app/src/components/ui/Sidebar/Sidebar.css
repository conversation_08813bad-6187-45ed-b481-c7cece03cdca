@import url(../../../variables.css);

:root {
    --sidebar-width: 260px;
    --sidebar-transition-duration: 0.3s;
}

.custom-sidebar,
#app-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: 100vh;
    z-index: 100;
    background-color: var(--surface-section);
    border-right: 1px solid var(--surface-border);
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;
    will-change: left;
    transition: left var(--sidebar-transition-duration) cubic-bezier(0.4, 0, 0.2, 1);
}

/* Main content push effect */
.main-content {
    transition: margin-left var(--sidebar-transition-duration) cubic-bezier(0.4, 0, 0.2, 1);
    margin-left: 0;
}

.main-content.sidebar-pushed {
    margin-left: var(--sidebar-width);
}

/* Sidebar structure */
.sidebar-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* Sidebar header */
.sidebar-header {
    display: flex;
    align-items: center;
    padding: 1.5rem 1rem;
    border-bottom: 1px solid var(--border-primary);
    background-color: var(--bg-primary);
    position: sticky;
    top: 0;
    z-index: 1;
}

/* Sidebar menu area */
.sidebar-menu-container {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.sidebar-menu {
    background: transparent;
    border: none;
    width: 100%;
}

.sidebar-menu .p-menuitem-link {
    padding: 0.75rem 1rem;
    color: var(--text-color-secondary);
    transition: all 0.2s ease;
    border-radius: var(--border-radius);
    margin: 0 0.5rem;
}

.sidebar-menu .p-menuitem-link:hover {
    background-color: var(--surface-hover);
    color: var(--text-color);
}

.sidebar-menu .p-menuitem-icon {
    font-size: 1.25rem;
    margin-right: 0.75rem;
    color: var(--text-color-secondary);
    transition: color 0.2s ease;
}

/* Active state */
.sidebar-menu .p-menuitem.p-highlight>.p-menuitem-link,
.active-menu-item {
    background-color: var(--surface-hover);
    color: var(--text-color);
    padding-left: calc(0.75rem - 3px); /* Adjust padding to account for border */
}

/* Scrollbar styling */
.overflow-y-auto::-webkit-scrollbar {
    width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
    background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
    background-color: var(--surface-border);
    border-radius: var(--border-radius-full);
}

/* Sidebar divider */
.sidebar-divider {
    height: 1px;
    background: linear-gradient(to right, transparent, var(--surface-border), transparent);
    margin-left: 1rem;
    margin-right: 1rem;
    position: relative;
}

.sidebar-divider::after {
    content: '';
    position: absolute;
    top: 1px;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.7), transparent);
}