import React, { useRef } from 'react';
import { Menu } from 'primereact/menu';
import { Ripple } from 'primereact/ripple';
import { useNavigate, useLocation } from '@tanstack/react-router';
import { MenuItem } from 'primereact/menuitem';
import { useSidebar } from '@/hooks/useSidebar';
import { sidebarConfig, SidebarConfigItem } from '@/config/sidebarConfig';
import './Sidebar.css';
import Button from '@/components/ui/Button';
import Typography from '@/components/ui/Typography';

export const SIDEBAR_WIDTH = 260; // px

export const AppSidebar: React.FC = () => {
    const { isOpen, closeSidebar } = useSidebar();
    const navigate = useNavigate();
    const location = useLocation();
    const menuRefs = useRef<Map<string, React.RefObject<HTMLDivElement>>>(new Map());
    const appName = "Avinya Ops"; // Replace with your app name

    // Check if a menu item is active
    const isMenuItemActive = (itemPath: string | undefined): boolean => {
        if (!itemPath) return false;

        // Check if the current path exactly matches the menu item path
        if (location.pathname === itemPath) return true;

        // Check if the current path is a child of the menu item path
        // This ensures parent menu items are highlighted when a child route is active
        return location.pathname.startsWith(itemPath + '/');
    };

    // Check if a parent menu item should be active based on its children
    const isParentMenuItemActive = (item: SidebarConfigItem): boolean => {
        if (item.to && isMenuItemActive(item.to)) return true;

        if (item.items) {
            // Check if any child item is active
            return item.items.some(childItem => {
                if (childItem.to && isMenuItemActive(childItem.to)) return true;
                if (childItem.items) return isParentMenuItemActive(childItem);
                return false;
            });
        }

        return false;
    };

    const transformMenuItems = (items: SidebarConfigItem[]): MenuItem[] => {
        return items.map(item => {
            const itemLabel = item.label || '';
            if (!menuRefs.current.has(itemLabel)) {
                menuRefs.current.set(itemLabel, React.createRef<HTMLDivElement>());
            }

            // Determine if this item should be highlighted
            const isActive = isParentMenuItemActive(item);

            return {
                label: itemLabel,
                icon: item.icon,
                command: item.to && !item.items ? () => navigate({ to: item.to! }) : undefined,
                items: item.items ? transformMenuItems(item.items) : undefined,
                className: isActive ? 'p-highlight' : '',
                template: (menuItem) => {
                    const ref = menuRefs.current.get(itemLabel);
                    if (!ref) return null;

                    return (
                        <div
                            className={`p-ripple flex align-items-center cursor-pointer p-3 border-round transition-duration-150 transition-colors w-full ${isActive ? 'active-menu-item' : 'text-700 hover:surface-100'}`}
                            onClick={() => {
                                if (item.to && !item.items) {
                                    navigate({ to: item.to });
                                }
                            }}
                        >
                            <i className={`${menuItem.icon} mr-2`}></i>
                            <Typography className="font-medium">{menuItem.label}</Typography>
                            {menuItem.items && <i className="pi pi-chevron-down ml-auto mr-1"></i>}
                            <Ripple />
                        </div>
                    );
                }
            };
        });
    };

    const menuItems = transformMenuItems(sidebarConfig);

    // Push effect: add a class to body or main-content when sidebar is open
    React.useEffect(() => {
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            if (isOpen) {
                mainContent.classList.add('sidebar-pushed');
            } else {
                mainContent.classList.remove('sidebar-pushed');
            }
        }
    }, [isOpen]);

    return (
        <div>
            <div
                id="app-sidebar"
                className={`custom-sidebar surface-section h-screen flex-shrink-0 border-right-1 surface-border select-none pt-2
                    ${isOpen ? 'sidebar-open' : 'sidebar-closed'}
                `}
                style={{
                    width: SIDEBAR_WIDTH,
                    left: isOpen ? 0 : -SIDEBAR_WIDTH,
                }}
            >
                <div className="flex flex-column h-full">
                    <div className="flex align-items-center justify-content-between px-4 pt-3 flex-shrink-0">
                        <Typography color='theme' className="font-semibold text-2xl">{appName}</Typography>
                        <Button
                            type="button"
                            variant='outline'
                            onClick={closeSidebar}
                            icon="pi pi-times"
                            className="h-2rem w-2rem"
                        />
                    </div>
                    <div className="sidebar-divider mt-3"></div>
                    <div className="overflow-y-auto">
                        <Menu
                            model={menuItems}
                            className="sidebar-menu border-none"
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};
