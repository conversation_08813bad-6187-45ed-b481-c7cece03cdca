import { Toast as PrimeToast } from 'primereact/toast';
import { forwardRef, useImperativeHandle, useRef } from 'react';

interface ToastProps {
    position?: 'top-right' | 'top-left' | 'top-center' | 'bottom-right' | 'bottom-left' | 'bottom-center' | 'center';
    className?: string;
}

export interface ToastRef {
    showSuccess: (message: string, title?: string) => void;
    showError: (message: string, title?: string) => void;
    showInfo: (message: string, title?: string) => void;
    showWarn: (message: string, title?: string) => void;
    showCustom: (message: string, title: string, severity: 'success' | 'error' | 'info' | 'warn', life?: number) => void;
}

const Toast = forwardRef<ToastRef, ToastProps>(({ position = 'top-right', className = '' }, ref) => {
    const toastRef = useRef<PrimeToast>(null);

    useImperativeHandle(ref, () => ({
        showSuccess: (message: string, title?: string) => {
            toastRef.current?.show({
                severity: 'success',
                summary: title || 'Success',
                detail: message,
                life: 3000
            });
        },
        showError: (message: string, title?: string) => {
            toastRef.current?.show({
                severity: 'error',
                summary: title || 'Error',
                detail: message,
                life: 5000
            });
        },
        showInfo: (message: string, title?: string) => {
            toastRef.current?.show({
                severity: 'info',
                summary: title || 'Information',
                detail: message,
                life: 3000
            });
        },
        showWarn: (message: string, title?: string) => {
            toastRef.current?.show({
                severity: 'warn',
                summary: title || 'Warning',
                detail: message,
                life: 4000
            });
        },
        showCustom: (message: string, title: string, severity: 'success' | 'error' | 'info' | 'warn', life?: number) => {
            toastRef.current?.show({
                severity,
                summary: title,
                detail: message,
                life: life || 3000
            });
        }
    }));

    return (
        <PrimeToast
            ref={toastRef}
            position={position}
            className={`custom-toast ${className}`}
        />
    );
});

Toast.displayName = 'Toast';

export default Toast; 