import Button from '@components/ui/Button';
import Breadcrumb from '@/components/ui/Breadcrumb';
import { useSidebar } from '@/hooks/useSidebar';
import React from 'react';


export const AppHeader: React.FC = () => {
  const { isOpen, toggleSidebar } = useSidebar();

  const ToolbarActions: React.FC = () => {
    return (
      <div>
       {/* add right component for header here */}
      </div>
    )
  }

  return (
    <>
      {!isOpen && <Button
        variant='outline'
        icon="pi pi-bars"
        onClick={toggleSidebar}
      />}
      <div className="flex-grow-1">
        <Breadcrumb />
      </div>
      <ToolbarActions />
    </>
  );
}; 