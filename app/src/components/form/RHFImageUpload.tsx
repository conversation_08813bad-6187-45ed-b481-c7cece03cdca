import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import ImageUpload from './ImageUpload/ImageUpload';

interface RHFImageUploadProps {
  name: string;
  label?: string;
  folder?: string;
  maxSizeInMB?: number;
  acceptedFileTypes?: string;
}

export const RHFImageUpload: React.FC<RHFImageUploadProps> = ({
  name,
  label,
  folder = 'products',
  maxSizeInMB = 2,
  acceptedFileTypes = 'image/*',
}) => {
  const { control, formState: { errors } } = useFormContext();
  const errorMessage = errors[name]?.message as string | undefined;

  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <ImageUpload
          label={label}
          initialValue={field.value}
          onChange={(value) => field.onChange(value)}
          folder={folder}
          maxSizeInMB={maxSizeInMB}
          acceptedFileTypes={acceptedFileTypes}
          error={errorMessage}
        />
      )}
    />
  );
};
