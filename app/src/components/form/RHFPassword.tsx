import { useFormContext, Controller } from 'react-hook-form';
import PasswordField from '@/components/ui/Form/PasswordField/PasswordField';
import type { PasswordFieldProps } from '@/components/ui/Form/PasswordField/PasswordField';

interface RHFPasswordProps extends Omit<PasswordFieldProps, 'value' | 'onChange'> {
  name: string;
  label?: string;
}

export const RHFPassword = ({ name, label, ...rest }: RHFPasswordProps) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const error = errors[name]?.message as string | undefined;

  return (
    <div className={!!error ? "" : "field mb-3"}>
      {label && <label htmlFor={name} className="block mb-1">{label}</label>}
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <PasswordField
            id={name}
            {...rest}
            {...field}
            error={!!error}
            errorMessage={error}
            className="w-full"
          />
        )}
      />
    </div>
  );
};