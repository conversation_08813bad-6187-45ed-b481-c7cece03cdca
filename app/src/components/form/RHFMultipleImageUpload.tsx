import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import MultipleImageUpload from './MultipleImageUpload/MultipleImageUpload';

interface RHFMultipleImageUploadProps {
  name: string;
  label?: string;
  maxSizeInMB?: number;
  acceptedFileTypes?: string;
  maxFiles?: number;
}

export const RHFMultipleImageUpload: React.FC<RHFMultipleImageUploadProps> = ({
  name,
  label,
  maxSizeInMB = 2,
  acceptedFileTypes = 'image/*',
  maxFiles = 5,
}) => {
  const { control, formState: { errors } } = useFormContext();
  const errorMessage = errors[name]?.message as string | undefined;

  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <MultipleImageUpload
          label={label}
          initialValue={field.value}
          onChange={(value) => field.onChange(value)}
          maxSizeInMB={maxSizeInMB}
          acceptedFileTypes={acceptedFileTypes}
          maxFiles={maxFiles}
          error={errorMessage}
        />
      )}
    />
  );
};
