import { useFormContext, RegisterOptions, Controller } from 'react-hook-form';
import IconField from '@/components/ui/Form/IconField/IconField';
import type { IconFieldProps } from '@/components/ui/Form/IconField/IconField';

interface RHFInputTextProps extends Omit<IconFieldProps, 'value' | 'onChange'> {
  name: string;
  label?: string;
  rules?: RegisterOptions;
}

export const RHFInputText = ({ name, label, rules, ...rest }: RHFInputTextProps) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const error = errors[name]?.message as string | undefined;

  return (
    <div className="field mb-3">
      {label && <label htmlFor={name} className="block mb-1">{label}</label>}
      <Controller
        name={name}
        control={control}
        rules={rules}
        render={({ field }) => (
          <IconField
            id={name}
            {...rest}
            {...field}
            error={!!error}
            errorMessage={error}
            className={error ? 'p-invalid w-full' : 'w-full'}
          />
        )}
      />
    </div>
  );
};