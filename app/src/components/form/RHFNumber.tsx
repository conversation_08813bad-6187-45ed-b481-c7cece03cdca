import { useForm<PERSON>ontext, Controller, RegisterOptions } from 'react-hook-form';
import IconField from '@/components/ui/Form/IconField/IconField';
import type { IconFieldProps } from '@/components/ui/Form/IconField/IconField';

interface RHFNumberProps extends Omit<IconFieldProps, 'value' | 'onChange' | 'onBlur' | 'ref'> {
  name: string;
  label?: string;
  rules?: RegisterOptions;
  min?: number;
  max?: number;
  step?: number;
}

export const RHFNumber = ({ name, label, rules, min, max, step, ...rest }: RHFNumberProps) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const error = errors[name]?.message as string | undefined;

  return (
    <div className="field mb-3">
      {label && <label htmlFor={name} className="block mb-1">{label}</label>}
      <Controller
        name={name}
        control={control}
        rules={rules}
        render={({ field }) => (
          <IconField
            id={name}
            keyfilter={'int'}
            min={min}
            max={max}
            step={step}
            {...rest}
            {...field}
            error={!!error}
            errorMessage={error}
            className={error ? 'p-invalid w-full' : 'w-full'}
          />
        )}
      />
    </div>
  );
};
