import { ReactNode } from 'react';
import { Form<PERSON>rovider, UseFormReturn } from 'react-hook-form';

interface RHFFormProviderProps {
  children: ReactNode;
  methods: UseFormReturn<any>;
  onSubmit: (data: any) => void;
  className?: string;
  autoComplete?: string;
}

export const RHFFormProvider = ({ children, methods, onSubmit, className, autoComplete = 'off' }: RHFFormProviderProps) => (
  <FormProvider {...methods}>
    <form onSubmit={methods.handleSubmit(onSubmit)} className={className} autoComplete={autoComplete}>
      {children}
    </form>
  </FormProvider>
); 