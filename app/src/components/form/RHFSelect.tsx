import { useForm<PERSON>ontex<PERSON>, Controller, RegisterOptions } from 'react-hook-form';
import { Dropdown } from 'primereact/dropdown';

interface RHFSelectProps {
  name: string;
  label?: string;
  placeholder?: string;
  icon?: string;
  options: string[] | { label: string; value: string }[];
  disabled?: boolean;
  className?: string;
  rules?: RegisterOptions;
  filter?: boolean;
}

export const RHFSelect = ({
  name,
  label,
  options,
  rules,
  placeholder,
  icon,
  filter = false,
  ...rest
}: RHFSelectProps) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const error = errors[name]?.message as string | undefined;

  // Convert options to the format expected by Dropdown
  const dropdownOptions = Array.isArray(options)
    ? options.map(option => {
        if (typeof option === 'string') {
          return { label: option, value: option };
        }
        return option;
      })
    : [];

  return (
    <div className="field mb-3">
      {label && <label htmlFor={name} className="block mb-1">{label}</label>}
      <Controller
        name={name}
        control={control}
        rules={rules}
        render={({ field }) => (
          <div className={`p-inputgroup ${error ? 'p-invalid' : ''}`}>
            {icon && <span className="p-inputgroup-addon"><i className={`pi ${icon}`}></i></span>}
            <Dropdown
              id={name}
              value={field.value}
              options={dropdownOptions}
              onChange={(e) => field.onChange(e.value)}
              placeholder={placeholder || `Select ${label}`}
              className={`w-full ${error ? 'p-invalid' : ''}`}
              filter={filter}
              {...rest}
            />
          </div>
        )}
      />
      {error && <small className="p-error">{error}</small>}
    </div>
  );
};
