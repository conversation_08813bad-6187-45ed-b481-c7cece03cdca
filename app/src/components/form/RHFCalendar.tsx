import { useFormContext, Controller, RegisterOptions } from 'react-hook-form';
import { Calendar } from 'primereact/calendar';
import './RHFCalendar.css';

interface RHFCalendarProps {
  name: string;
  label?: string;
  placeholder?: string;
  icon?: string;
  disabled?: boolean;
  className?: string;
  rules?: RegisterOptions;
  dateFormat?: string;
  minDate?: Date;
  maxDate?: Date;
  showTime?: boolean;
  timeOnly?: boolean;
  hourFormat?: '12' | '24';
  showButtonBar?: boolean;
  monthNavigator?: boolean;
  yearNavigator?: boolean;
  yearRange?: string;
  readOnlyInput?: boolean;
  dateRestriction?: {
    range: 'past' | 'future' | 'all'; // Date range restriction: past, future, or all dates
    includeToday?: boolean; // Whether to include today in the range
  };
}

export const RHFCalendar = ({
  name,
  label,
  rules,
  placeholder,
  icon,
  dateFormat = 'mm/dd/yy',
  dateRestriction = { range: 'all', includeToday: true },
  minDate: propMinDate,
  maxDate: propMaxDate,
  ...rest
}: RHFCalendarProps) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const error = errors[name]?.message as string | undefined;

  // Calculate min and max dates based on dateRestriction
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Reset time to start of day

  // Determine minDate and maxDate based on dateRestriction
  // If props are provided, they take precedence over dateRestriction
  let minDate = propMinDate;
  let maxDate = propMaxDate;

  if (!propMinDate && !propMaxDate) {
    const range = dateRestriction.range;
    // Default includeToday to false for past/future and true for 'all'
    const includeToday = dateRestriction.includeToday !== undefined
      ? dateRestriction.includeToday
      : range === 'all';

    if (range === 'past') {
      if (includeToday) {
        // For past dates including today, set maxDate to today
        maxDate = today;
      } else {
        // For past dates excluding today, set maxDate to yesterday
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        maxDate = yesterday;
      }
    } else if (range === 'future') {
      if (includeToday) {
        // For future dates including today, set minDate to today
        minDate = today;
      } else {
        // For future dates excluding today, set minDate to tomorrow
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        minDate = tomorrow;
      }
    }
  }

  return (
    <div className="field mb-3">
      {label && <label htmlFor={name} className="block mb-1">{label}</label>}
      <Controller
        name={name}
        control={control}
        rules={rules}
        render={({ field }) => (
          <div className={`p-inputgroup ${error ? 'p-invalid' : ''}`}>
            {icon && <span className="p-inputgroup-addon"><i className={`pi ${icon}`}></i></span>}
            <Calendar
              id={name}
              value={field.value}
              onChange={(e) => field.onChange(e.value)}
              onBlur={field.onBlur}
              placeholder={placeholder || `Select ${label}`}
              dateFormat={dateFormat}
              className={`w-full ${error ? 'p-invalid' : ''}`}
              inputClassName="w-full"
              minDate={minDate}
              maxDate={maxDate}
              {...rest}
            />
          </div>
        )}
      />
      {error && <small className="p-error">{error}</small>}
    </div>
  );
};
