import React, { useState, useRef, useEffect } from 'react';
import { FileUpload } from 'primereact/fileupload';
import Button from '@/components/ui/Button/Button';
import { useUploadFile, useDeleteFile } from '@/hooks/useObjectStorage';
import './ImageUpload.css';

interface ImageUploadProps {
  label?: string;
  initialValue?: string;
  onChange: (imagePath: string | null) => void;
  folder?: string;
  maxSizeInMB?: number;
  acceptedFileTypes?: string;
  error?: string;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  label = 'Image',
  initialValue,
  onChange,
  folder = 'products',
  maxSizeInMB = 2,
  acceptedFileTypes = 'image/*',
  error,
}) => {
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const fileUploadRef = useRef<FileUpload>(null);

  const uploadFileMutation = useUploadFile();
  const deleteFileMutation = useDeleteFile();

  // Convert MB to bytes
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024;

  useEffect(() => {
    if (initialValue) {
      // Check if initialValue is a full URL or just a key
      if (initialValue.startsWith('http')) {
        // It's already a URL, use it directly
        setImageUrl(initialValue);
      } else {
        // It's a key, we need to get the URL
        // For mock implementation, we'll use a placeholder
        const imageId = Math.floor(Math.random() * 1000);
        const mockUrl = `https://source.unsplash.com/random/300x300?sig=${imageId}`;
        setImageUrl(mockUrl);
      }
    } else {
      setImageUrl(null);
    }
  }, [initialValue]);

  const handleFileUpload = async (event: { files: File[] }) => {
    const file = event.files[0];

    if (!file) return;

    // Validate file size
    if (file.size > maxSizeInBytes) {
      alert(`File size exceeds the maximum allowed size of ${maxSizeInMB}MB.`);
      return;
    }

    setIsLoading(true);

    try {
      const response = await uploadFileMutation.mutateAsync({
        file,
        options: {
          folder,
          public: true,
        },
      });

      const uploadedFile = response.data;
      // Store the URL for display
      setImageUrl(uploadedFile.url || null);
      // For consistency with the mock data, we'll store the URL instead of the key
      onChange(uploadedFile.url || uploadedFile.key);
    } catch (error) {
      console.error('Error uploading file:', error);
      alert('Failed to upload image. Please try again.');
    } finally {
      setIsLoading(false);
      // Clear the file input
      if (fileUploadRef.current) {
        fileUploadRef.current.clear();
      }
    }
  };

  const handleRemoveImage = async () => {
    if (!imageUrl || !initialValue) {
      setImageUrl(null);
      onChange(null);
      return;
    }

    setIsLoading(true);

    try {
      // Extract the key from the URL or use the initialValue as the key
      // For full URLs, we'll just simulate deletion
      // For keys, we'll call the delete API
      const key = initialValue.startsWith('http')
        ? initialValue.split('/').pop() || initialValue // Extract filename from URL as mock key
        : initialValue;

      await deleteFileMutation.mutateAsync({ key });

      setImageUrl(null);
      onChange(null);
    } catch (error) {
      console.error('Error deleting file:', error);
      alert('Failed to remove image. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="image-upload-container">
      {label && <label className="image-upload-label">{label}</label>}

      <div className="image-upload-preview">
        {imageUrl ? (
          <img src={imageUrl} alt="Preview" />
        ) : (
          <div className="image-upload-placeholder">
            <i className="pi pi-image"></i>
            <span>No image selected</span>
          </div>
        )}
      </div>

      <div className="image-upload-actions">
        <FileUpload
          ref={fileUploadRef}
          mode="basic"
          name="image"
          url="/api/dummy-url" // This is not used as we handle the upload manually
          accept={acceptedFileTypes}
          maxFileSize={maxSizeInBytes}
          auto={true}
          chooseLabel="Select Image"
          onSelect={handleFileUpload}
          disabled={isLoading}
          className="p-button-sm"
        />

        {imageUrl && (
          <Button
            variant="danger"
            size="small"
            onClick={handleRemoveImage}
            isLoading={isLoading}
            leftIcon={<i className="pi pi-trash"></i>}
          >
            Remove
          </Button>
        )}
      </div>

      {error && <div className="image-upload-error">{error}</div>}
    </div>
  );
};

export default ImageUpload;
