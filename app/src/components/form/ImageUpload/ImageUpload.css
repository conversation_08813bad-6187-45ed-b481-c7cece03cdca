@import url(../../../variables.css);

.image-upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1.5rem;
}

.image-upload-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.image-upload-preview {
  width: 150px;
  height: 150px;
  border-radius: 4px;
  border: 1px dashed var(--border-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-bottom: 0.5rem;
  background-color: var(--surface-ground);
}

.image-upload-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.image-upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  font-size: 0.875rem;
  text-align: center;
  padding: 1rem;
}

.image-upload-placeholder i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.image-upload-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.image-upload-actions .p-button{
  background-color: var(--btn-primary-bg);
  border: none;
}

.image-upload-actions .button--danger{
  background-color: var(--btn-danger-bg);
  border: none;
}

.image-upload-error {
  color: var(--error-color);
  font-size: 0.75rem;
  margin-top: 0.25rem;
}
