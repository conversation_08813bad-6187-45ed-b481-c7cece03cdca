import { Checkbox, CheckboxProps } from 'primereact/checkbox';
import { useFormContext, RegisterOptions } from 'react-hook-form';

interface RHFCheckboxProps extends CheckboxProps {
  name: string;
  label?: string;
  rules?: RegisterOptions;
}

export const RHFCheckbox = ({ name, label, rules, ...rest }: RHFCheckboxProps) => {
  const {
    register,
    formState: { errors },
  } = useFormContext();

  const error = errors[name]?.message as string | undefined;

  return (
    <div className="field-checkbox mb-2">
      <Checkbox id={name} inputId={name} {...register(name, rules)} {...rest} />
      {label && <label htmlFor={name} className="ml-2">{label}</label>}
      {error && <small className="p-error">{error}</small>}
    </div>
  );
}; 