@import url(../../../variables.css);

.multiple-image-upload-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
}

.multiple-image-upload-label {
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  display: block;
}

/* Image Previews */
.multiple-image-upload-previews {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.image-previews-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 1rem;
  max-height: 300px;
  overflow-y: auto;
  padding: 1rem;
  border: 2px dashed var(--border-primary);
  border-radius: 8px;
  background-color: var(--surface-ground);
}

.image-preview-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid var(--border-primary);
  background-color: var(--surface-card);
  transition: all 0.2s ease;
}

.image-preview-item:hover {
  border-color: var(--btn-primary-bg);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.image-preview-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.image-remove-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 50%;
  background-color: rgba(239, 68, 68, 0.9);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.2s ease;
  z-index: 1;
}

.image-remove-btn:hover {
  background-color: rgba(239, 68, 68, 1);
  transform: scale(1.1);
}

.image-remove-btn:focus {
  outline: 2px solid var(--btn-primary-bg);
  outline-offset: 2px;
}

.image-count-info {
  font-size: 0.875rem;
  color: var(--text-secondary);
  text-align: center;
  padding: 0.5rem;
  background-color: var(--surface-card);
  border-radius: 6px;
  border: 1px solid var(--border-primary);
}

/* Upload Actions */
.multiple-image-upload-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.upload-button-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.multiple-image-upload-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.image-upload-select-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 8px;
  background-color: var(--btn-primary-bg);
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
}

.image-upload-select-btn:hover:not(:disabled) {
  background-color: var(--btn-primary-bg-hover);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.image-upload-select-btn:disabled {
  background-color: var(--surface-300);
  color: var(--text-secondary);
  cursor: not-allowed;
  opacity: 0.6;
}

.image-upload-select-btn:focus {
  outline: 2px solid var(--btn-primary-bg);
  outline-offset: 2px;
}

/* Error Message */
.multiple-image-upload-error {
  color: var(--error-color);
  font-size: 0.875rem;
  margin-top: 0.25rem;
  padding: 0.5rem;
  background-color: var(--error-bg);
  border: 1px solid var(--error-border);
  border-radius: 6px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .image-previews-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 0.75rem;
    padding: 0.75rem;
  }

  .multiple-image-upload-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .upload-button-wrapper {
    justify-content: center;
  }
}

/* Empty State */
.multiple-image-upload-container:not(:has(.image-previews-grid)) .multiple-image-upload-actions {
  justify-content: center;
  padding: 2rem;
  border: 2px dashed var(--border-primary);
  border-radius: 8px;
  background-color: var(--surface-ground);
}

/* Loading State */
.multiple-image-upload-container.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Focus States for Accessibility */
.multiple-image-upload-container:focus-within {
  outline: 2px solid var(--btn-primary-bg);
  outline-offset: 2px;
  border-radius: 8px;
}
