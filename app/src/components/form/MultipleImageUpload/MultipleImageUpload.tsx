import React, { useState, useRef, useEffect } from 'react';
import { FileUpload } from 'primereact/fileupload';
import Button from '@/components/ui/Button/Button';
import './MultipleImageUpload.css';

interface MultipleImageUploadProps {
  label?: string;
  initialValue?: File[] | string[] | null;
  onChange: (files: File[] | null) => void;
  maxSizeInMB?: number;
  acceptedFileTypes?: string;
  error?: string;
  maxFiles?: number;
}

interface ImagePreview {
  id: string;
  file: File;
  url: string;
}

const MultipleImageUpload: React.FC<MultipleImageUploadProps> = ({
  label = 'Product Images',
  initialValue,
  onChange,
  maxSizeInMB = 2,
  acceptedFileTypes = 'image/*',
  error,
  maxFiles = 5,
}) => {
  const [selectedImages, setSelectedImages] = useState<ImagePreview[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const fileUploadRef = useRef<FileUpload>(null);

  // Convert MB to bytes
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024;

  // Initialize with existing images if provided
  useEffect(() => {
    if (initialValue && Array.isArray(initialValue)) {
      // Handle case where initialValue is an array of File objects
      if (initialValue.length > 0 && initialValue[0] instanceof File) {
        const fileArray = initialValue as File[];
        const previews: ImagePreview[] = fileArray.map((file, index) => ({
          id: `existing-${index}`,
          file,
          url: URL.createObjectURL(file),
        }));
        setSelectedImages(previews);
      }
      // Note: We don't handle string URLs as initial values since we need File objects for upload
    }
  }, [initialValue]);

  // Clean up object URLs when component unmounts or images change
  useEffect(() => {
    return () => {
      selectedImages.forEach(image => {
        if (image.url.startsWith('blob:')) {
          URL.revokeObjectURL(image.url);
        }
      });
    };
  }, [selectedImages]);

  const handleFileSelect = (event: { files: File[] }) => {
    const files = event.files;

    if (!files || files.length === 0) return;

    // Check if adding these files would exceed the max limit
    if (selectedImages.length + files.length > maxFiles) {
      alert(`You can only upload a maximum of ${maxFiles} images. Currently selected: ${selectedImages.length}`);
      return;
    }

    const validFiles: File[] = [];
    const invalidFiles: string[] = [];

    // Validate each file
    files.forEach(file => {
      if (file.size > maxSizeInBytes) {
        invalidFiles.push(`${file.name} (exceeds ${maxSizeInMB}MB)`);
      } else {
        validFiles.push(file);
      }
    });

    // Show error for invalid files
    if (invalidFiles.length > 0) {
      alert(`The following files exceed the maximum size limit:\n${invalidFiles.join('\n')}`);
    }

    // Process valid files
    if (validFiles.length > 0) {
      const newPreviews: ImagePreview[] = validFiles.map((file, index) => ({
        id: `new-${Date.now()}-${index}`,
        file,
        url: URL.createObjectURL(file),
      }));

      const updatedImages = [...selectedImages, ...newPreviews];
      setSelectedImages(updatedImages);
      
      // Update parent component with File array
      const fileArray = updatedImages.map(img => img.file);
      onChange(fileArray.length > 0 ? fileArray : null);
    }

    // Clear the file input
    if (fileUploadRef.current) {
      fileUploadRef.current.clear();
    }
  };

  const handleRemoveImage = (imageId: string) => {
    const imageToRemove = selectedImages.find(img => img.id === imageId);
    if (imageToRemove && imageToRemove.url.startsWith('blob:')) {
      URL.revokeObjectURL(imageToRemove.url);
    }

    const updatedImages = selectedImages.filter(img => img.id !== imageId);
    setSelectedImages(updatedImages);
    
    // Update parent component with File array
    const fileArray = updatedImages.map(img => img.file);
    onChange(fileArray.length > 0 ? fileArray : null);
  };

  const handleRemoveAll = () => {
    // Clean up all object URLs
    selectedImages.forEach(image => {
      if (image.url.startsWith('blob:')) {
        URL.revokeObjectURL(image.url);
      }
    });

    setSelectedImages([]);
    onChange(null);
  };

  return (
    <div className="multiple-image-upload-container">
      {label && <label className="multiple-image-upload-label">{label}</label>}

      {/* Image Previews Grid */}
      {selectedImages.length > 0 && (
        <div className="multiple-image-upload-previews">
          <div className="image-previews-grid">
            {selectedImages.map((image) => (
              <div key={image.id} className="image-preview-item">
                <img src={image.url} alt="Preview" />
                <button
                  type="button"
                  className="image-remove-btn"
                  onClick={() => handleRemoveImage(image.id)}
                  title="Remove image"
                >
                  <i className="pi pi-times"></i>
                </button>
              </div>
            ))}
          </div>
          
          <div className="image-count-info">
            {selectedImages.length} of {maxFiles} images selected
          </div>
        </div>
      )}

      {/* Upload Actions */}
      <div className="multiple-image-upload-actions">
        <div className="upload-button-wrapper">
          <FileUpload
            ref={fileUploadRef}
            mode="basic"
            name="images"
            url="/api/dummy-url" // This is not used as we handle the upload manually
            accept={acceptedFileTypes}
            maxFileSize={maxSizeInBytes}
            multiple={true}
            auto={true}
            chooseLabel=""
            onSelect={handleFileSelect}
            disabled={isLoading || selectedImages.length >= maxFiles}
            className="multiple-image-upload-input"
          />
          <button
            type="button"
            className="image-upload-select-btn"
            onClick={() => fileUploadRef.current?.getInput()?.click()}
            disabled={selectedImages.length >= maxFiles}
            title={selectedImages.length >= maxFiles ? `Maximum ${maxFiles} images allowed` : "Select Images"}
          >
            <i className="pi pi-upload"></i>
          </button>
        </div>

        {selectedImages.length > 0 && (
          <Button
            variant="danger"
            size="small"
            onClick={handleRemoveAll}
            isLoading={isLoading}
            leftIcon={<i className="pi pi-trash"></i>}
          >
            Remove All
          </Button>
        )}
      </div>

      {error && <div className="multiple-image-upload-error">{error}</div>}
    </div>
  );
};

export default MultipleImageUpload;
