.file-upload-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.file-upload-label {
  font-weight: 500;
  color: var(--text-color);
  font-size: 14px;
  margin-bottom: 4px;
}

.file-upload-preview {
  width: 200px;
  height: 150px;
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: var(--surface-ground);
}

.file-upload-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.file-upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: var(--text-color-secondary);
  text-align: center;
}

.file-upload-placeholder i {
  font-size: 24px;
  color: var(--text-color-secondary);
}

.file-upload-placeholder span {
  font-size: 12px;
}

.file-upload-placeholder span.delete-message {
  color: var(--red-500);
  font-weight: 500;
}

.file-upload-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background-color: var(--surface-ground);
  border: 1px solid var(--border-color);
  border-radius: 6px;
}

.file-upload-info i {
  color: var(--primary-color);
  font-size: 16px;
}

.file-upload-info .file-size {
  color: var(--text-color-secondary);
  font-size: 12px;
  margin-left: auto;
}

.file-upload-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.file-upload-button-wrapper {
  position: relative;
  display: inline-block;
}

.file-upload-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
  overflow: hidden;
}

.file-upload-select-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 6px;
  background-color: var(--primary-color);
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.file-upload-select-btn:hover {
  background-color: var(--primary-color-dark, var(--primary-color));
  transform: translateY(-1px);
}

.file-upload-select-btn:active {
  transform: translateY(0);
}

.file-upload-select-btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.file-upload-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.file-upload-remove-btn {
  background-color: var(--red-500);
  color: white;
}

.file-upload-remove-btn:hover {
  background-color: var(--red-600, var(--red-500));
  transform: translateY(-1px);
}

.file-upload-delete-btn {
  background-color: var(--red-500);
  color: white;
}

.file-upload-delete-btn:hover {
  background-color: var(--red-600, var(--red-500));
  transform: translateY(-1px);
}

.file-upload-action-btn:active {
  transform: translateY(0);
}

.file-upload-action-btn:focus {
  outline: 2px solid var(--red-500);
  outline-offset: 2px;
}

.file-upload-error {
  color: var(--red-500);
  font-size: 12px;
  margin-top: 4px;
}

/* Hide PrimeReact FileUpload default button */
.file-upload-input .p-fileupload .p-button {
  display: none;
}

/* Responsive design */
@media (max-width: 768px) {
  .file-upload-preview {
    width: 150px;
    height: 120px;
  }

  .file-upload-actions {
    flex-direction: column;
    align-items: stretch;
  }
}
