/* PrimeReact Theme */
@import 'primereact/resources/themes/lara-light-indigo/theme.css';
@import 'primereact/resources/primereact.min.css';
@import 'primeicons/primeicons.css';
@import 'primeflex/primeflex.css';

/* Sakai Theme Overrides */
:root {
  --primary-color: var(--color-primary);
  --primary-color-text: #ffffff;
  --surface-ground: #ffffff; /* Changed from #f8f9fa to #ffffff to match page background */
  --surface-section: #ffffff;
  --surface-card: #ffffff;
  --surface-overlay: #ffffff;
  --surface-border: #dfe7ef;
  --surface-hover: #f6f9fc;
  --focus-ring: var(--color-primary);
  --maskbg: rgba(0, 0, 0, 0.4);
}