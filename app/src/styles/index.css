@import url('../variables.css');

body {
  margin: 0;
  box-sizing: border-box;
  min-width: 320px;
  min-height: 100vh;
  font-family: var(--main-font);
}

a {
  text-decoration: none;
}

/* Common Page Heading Style */
.page-heading {
  font-size: var(--page-heading-font-size);
  font-weight: var(--page-heading-font-weight);
  color: var(--page-heading-color);
  margin-top: 0;
  margin-bottom: var(--page-heading-margin-bottom);
  line-height: 1.2;
}

/* Responsive adjustments for page headings */
@media screen and (max-width: 768px) {
  .page-heading {
    font-size: var(--text-xl);
    margin-bottom: 1rem;
  }
}