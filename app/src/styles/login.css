.login-card {
  width: 100%;
  max-width: 400px;
  z-index: 1;
}

.login-title {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  text-align: center;
}

.login-subtitle {
  font-size: 1rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  text-align: center;
}

.login-form .p-inputtext,
.login-form .p-password-input {
  width: 100%;
  border-radius: 6px;
  font-size: 1rem;
}

.login-form .p-inputgroup {
  margin-bottom: 1rem;
}

.login-form .p-checkbox {
  margin-right: 0.5rem;
}

.login-form .login-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.login-form .login-reset {
  color: #6366f1;
  font-size: 0.95rem;
  text-decoration: none;
  cursor: pointer;
  transition: text-decoration 0.2s;
}

.login-form .login-reset:hover {
  text-decoration: underline;
}

.login-form .p-button {
  width: 100%;
  background: #6366f1;
  border: none;
  font-weight: 600;
  font-size: 1.1rem;
  border-radius: 6px;
}

@media (max-width: 500px) {
  .login-card {
    min-width: 0;
  }
} 