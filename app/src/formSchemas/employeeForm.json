{"fields": [{"type": "text", "name": "username", "label": "Username", "placeholder": "Enter username", "icon": "pi-user", "validation": {"required": true, "minLength": 10, "maxLength": 50}}, {"type": "text", "name": "firstName", "label": "First Name", "placeholder": "Enter first name", "icon": "pi-user", "validation": {"required": true, "minLength": 2, "maxLength": 50}}, {"type": "text", "name": "lastName", "label": "Last Name", "placeholder": "Enter last name", "icon": "pi-user", "validation": {"required": true, "minLength": 2, "maxLength": 50}}, {"type": "text", "name": "email", "label": "Email", "placeholder": "Enter email address", "icon": "pi-envelope", "validation": {"required": true, "email": true, "minLength": 8}}, {"type": "number", "name": "phone-number", "label": "Phone Number", "placeholder": "Enter phone number", "icon": "pi-phone", "validation": {"required": true}}, {"type": "password", "name": "password", "label": "Password", "placeholder": "Enter password", "icon": "pi-lock", "validation": {"required": true, "minLength": 8}}, {"type": "select", "name": "role", "label": "Role", "placeholder": "Select role", "icon": "pi-id-card", "options": [], "validation": {"required": true}}, {"type": "text", "name": "position", "label": "Position", "placeholder": "Enter position", "icon": "pi-briefcase", "validation": {"required": true, "minLength": 2, "maxLength": 100}}, {"type": "text", "name": "department", "label": "Department", "placeholder": "Enter department", "icon": "pi-building", "validation": {"required": true, "minLength": 2, "maxLength": 100}}, {"type": "select", "name": "department_id", "label": "Department ID", "placeholder": "Select department ID", "icon": "pi-building", "options": [], "validation": {"required": false}}, {"type": "date", "name": "joining_date", "label": "Joining Date", "placeholder": "Select joining date", "icon": "pi-calendar", "validation": {"required": false}}, {"type": "select", "name": "status", "label": "Status", "placeholder": "Select status", "icon": "pi-flag", "options": [], "validation": {"required": false}}, {"type": "date", "name": "termination_date", "label": "Termination Date", "placeholder": "Select termination date (if applicable)", "dateRestriction": {"range": "future", "includeToday": false}, "icon": "pi-calendar-times", "validation": {"required": false}}, {"type": "select", "name": "organization_id", "label": "Organization", "placeholder": "Select organization", "icon": "pi-sitemap", "options": [], "validation": {"required": false}}], "actions": [{"id": "submit", "type": "submit", "label": "Add Employee"}, {"id": "cancel", "type": "button", "label": "Cancel"}]}