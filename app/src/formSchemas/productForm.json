{"fields": [{"type": "text", "name": "name", "label": "Product Name", "placeholder": "Enter product name", "icon": "pi-tag", "validation": {"required": true, "minLength": 2, "maxLength": 100}}, {"type": "text", "name": "description", "label": "Description", "placeholder": "Enter product description", "icon": "pi-align-left", "validation": {"required": true, "maxLength": 500}}, {"type": "number", "name": "price", "label": "Price", "placeholder": "Enter product price", "icon": "pi-dollar", "validation": {"required": true, "min": 0.01}}, {"type": "select", "name": "categoryId", "label": "Category", "placeholder": "Select category", "icon": "pi-list", "options": [], "validation": {"required": true}}, {"type": "select", "name": "subCategoryId", "label": "Subcategory", "placeholder": "Select subcategory", "icon": "pi-sitemap", "options": [], "validation": {"required": true}}, {"type": "multipleImages", "name": "imageFiles", "label": "Product Images", "maxSizeInMB": 2, "acceptedFileTypes": "image/*", "maxFiles": 5, "validation": {"required": false}}], "actions": [{"id": "submit", "type": "submit", "label": "Save Product"}, {"id": "cancel", "type": "button", "label": "Cancel"}]}