{"fields": [{"type": "text", "name": "name", "label": "Location Name", "placeholder": "Enter location name", "icon": "pi-map-marker", "validation": {"required": true, "minLength": 2, "maxLength": 100}}, {"type": "text", "name": "timezone", "label": "Timezone", "placeholder": "Enter timezone (e.g., America/New_York)", "icon": "pi-clock", "validation": {"required": true, "minLength": 3, "maxLength": 50}}, {"type": "text", "name": "addressLine1", "label": "Address Line 1", "placeholder": "Enter address line 1", "icon": "pi-home", "validation": {"required": true, "minLength": 1, "maxLength": 100}}, {"type": "text", "name": "addressLine2", "label": "Address Line 2", "placeholder": "Enter address line 2 (optional)", "icon": "pi-home", "validation": {"required": false, "maxLength": 100}}, {"type": "text", "name": "city", "label": "City", "placeholder": "Enter city", "icon": "pi-building", "validation": {"required": true, "minLength": 1, "maxLength": 50}}, {"type": "text", "name": "state", "label": "State/Province", "placeholder": "Enter state or province", "icon": "pi-map", "validation": {"required": true, "minLength": 1, "maxLength": 50}}, {"type": "text", "name": "postalCode", "label": "Postal Code", "placeholder": "Enter postal code (optional)", "icon": "pi-envelope", "validation": {"required": false, "maxLength": 20}}, {"type": "text", "name": "countryCode", "label": "Country Code", "placeholder": "Enter 2-letter country code (e.g., US, GB)", "icon": "pi-globe", "validation": {"required": true, "minLength": 2, "maxLength": 2, "pattern": "^[A-Z]{2}$"}}], "actions": [{"id": "submit", "type": "submit", "label": "Save Location"}, {"id": "cancel", "type": "button", "label": "Cancel Now"}]}