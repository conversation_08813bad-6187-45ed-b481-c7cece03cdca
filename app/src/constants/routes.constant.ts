export const ROUTES = {
  // Public routes
  PUBLIC: {
    LOGIN: '/login',
  },
  // Private routes
  PRIVATE: {
    APP: '/app',
    DASHBOARD: '/dashboard',
    ORGANIZATION_DETAILS: '/organization/overview',
    EMPLOYEE_DETAILS: '/employee',
    LOCATION_DETAILS: '/location',
    DEPARTMENT_DETAILS: '/department',
    DESIGNATION_DETAILS: '/designation',
    ORGANIZATION_ITEM_CATALOG: '/organization/items',
    ADD_EMPLOYEE: '/employee/add',
    EDIT_EMPLOYEE: 'employee/edit',
    ADD_PRODUCT: '/product/add',
    EDIT_PRODUCT: '/product/edit',
    SETTINGS: '/organization/settings',
    ADD_CATEGORY: '/category/add',
    EDIT_CATEGORY: '/category/edit',
    ADD_SUBCATEGORY: '/subcategory/add',
    EDIT_SUBCATEGORY: '/subcategory/edit',
  },
  // Common routes
  COMMON: {
    NOT_FOUND: '*',
  },
} as const;

export type RoutePath = typeof ROUTES.PUBLIC[keyof typeof ROUTES.PUBLIC] |
  typeof ROUTES.PRIVATE[keyof typeof ROUTES.PRIVATE] |
  typeof ROUTES.COMMON[keyof typeof ROUTES.COMMON];