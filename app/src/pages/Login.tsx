import { useNavigate, useLocation, Navigate } from '@tanstack/react-router';
import { useAuth } from '@/hooks/useAuth';
import { DynamicForm } from '@/components/form/DynamicForm';
import loginFormSchemaJson from '@/formSchemas/loginForm.json';
import type { FormSchema } from '@/components/form/DynamicForm';
import '@/styles/login.css';
import { useState } from 'react';
import Card from '@/components/ui/Card/Card';
import { appRoute } from '@/routes/private/app.route';
import { dashboardRoute } from '@/routes/private/dashboard.route';

const loginFormSchema = loginFormSchemaJson as FormSchema;

export const Login = () => {
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, login } = useAuth();
  const from = location.state?.from?.pathname || dashboardRoute.to;

  if (isAuthenticated) {
    return <Navigate to={appRoute.to} />;
  }

  const handleSubmit = async (data: any) => {
    try {
      await login(data.email, data.password);
      navigate({ to: from, replace: true });
    } catch (err) {
      setError('Invalid email or password');
    }
  };

  return (
    <div className="flex align-items-center justify-content-center min-h-screen" style={{ position: 'relative' }}>
      <img
        src="/assets/wave-green-fullscreen.svg"
        alt="Wave Background"
        className="fixed left-0 top-0 min-h-screen min-w-screen"
        style={{ zIndex: -1 }}
      />
      <Card
        title="Log in"
        subtitle="Please enter your details"
        variant="elevated"
        padding="large"
        className="login-card"
      >
        <DynamicForm schema={loginFormSchema} onSubmit={handleSubmit} defaultValues={{ name: "", password: "" }} />
        {error && <div className="p-error mb-3" style={{ textAlign: 'center' }}>{error}</div>}
      </Card>
    </div>
  );
}; 