import { Link, Navigate } from '@tanstack/react-router';
import '@/styles/NotFound.css'
import withWaveBg from '@/hoc/withWaveBg';
import Button from '@/components/ui/Button';
import Typography from '@/components/ui/Typography';
import { useAuth } from '@/hooks/useAuth';
import { loginRoute } from '@/routes/public/login.route';
import { dashboardRoute } from '@/routes/private/dashboard.route';

const AuthAwareNotFound: React.FC = () => {
  const { isAuthenticated } = useAuth();

  if (isAuthenticated) {
    return <NotFound />;
  }

  return <Navigate to={loginRoute.to} />;
};

const NotFound: React.FC = () => {
  return (
    <div className="not-found">
      <Typography variant='h1' weight='bold' className='pb-2'>Oops!</Typography>
      <Typography variant='body1' weight='medium' color='secondary' className='pb-2'>There is nothing here</Typography>
      <Link to={dashboardRoute.to}>
        <Button variant="primary" size="medium">
          Go to Dashboard
        </Button>
      </Link>
    </div>
  );
};

export default withWaveBg(AuthAwareNotFound);