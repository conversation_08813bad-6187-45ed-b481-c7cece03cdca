import React, { useState, useRef, useMemo } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { DynamicForm } from '@/components/form/DynamicForm';
import employeeFormSchemaJson from '@/formSchemas/employeeForm.json';
import type { FormSchema } from '@/components/form/DynamicForm';
import { useCreateEmployee } from '@/hooks/useEmployee';
import { CreateEmployeeRequest, EmployeeRole, EmployeeStatus } from '@/types/employee';
import Card from '@/components/ui/Card/Card';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import Modal from '@/components/ui/Modal/Modal';
import { getEnumValues } from '@/utils/enumUtils';
import { employeeDetailsRoute } from '@/routes/private/employeeDetails.route';
import './AddEmployee.css';

// Helper function to safely format date values
const formatDateValue = (dateValue: any): string => {
    if (!dateValue) return '';

    if (dateValue instanceof Date) {
        return dateValue.toLocaleDateString();
    }

    if (typeof dateValue === 'string') {
        // If it's already a string, return it
        return dateValue;
    }

    // Fallback: convert to string
    return String(dateValue);
};

const AddEmployee: React.FC = () => {
    const navigate = useNavigate();
    const toast = useRef<ToastRef>(null);
    const [error, setError] = useState<string | null>(null);
    const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
    const [employeeData, setEmployeeData] = useState<CreateEmployeeRequest | null>(null);
    const createEmployeeMutation = useCreateEmployee();

    // Get enum values
    const employeeRoleValues = useMemo(() => getEnumValues(EmployeeRole), []);
    const employeeStatusValues = useMemo(() => getEnumValues(EmployeeStatus), []);

    // Create a dynamic form schema with the enum values
    const employeeFormSchema = useMemo(() => {
        // Clone the original schema
        const schema = JSON.parse(JSON.stringify(employeeFormSchemaJson)) as FormSchema;

        // Find the role field and update its options
        const roleField = schema.fields.find(field => field.name === 'role');
        if (roleField) {
            roleField.options = employeeRoleValues;
        }

        // Find the status field and update its options
        const statusField = schema.fields.find(field => field.name === 'status');
        if (statusField) {
            statusField.options = employeeStatusValues;
        }

        return schema;
    }, [employeeRoleValues, employeeStatusValues]);

    const handleSubmit = async (data: any) => {
        try {
            setError(null);

            // Format the data to match our CreateEmployeeRequest type
            const formattedData: CreateEmployeeRequest = {
                // User information
                username: data.username,
                firstName: data.firstName,
                lastName: data.lastName,
                email: data.email,
                phoneNumber: data['phone-number'], // Note the different naming in the form schema
                password: data.password,

                // Employee information
                position: data.position || 'Not specified',
                department: data.department || 'General',
                department_id: data.department_id,
                role: data.role as EmployeeRole,
                joining_date: data.joining_date ? formatDateValue(data.joining_date) : new Date().toISOString().split('T')[0], // Default to today
                organization_id: data.organization_id
            };

            // await createEmployeeMutation.mutateAsync(formattedData);

            // Show success message
            toast.current?.showSuccess('Employee successfully added to the system');

            // Store employee data for display in modal
            setEmployeeData(formattedData);

            // Show modal with employee information
            setIsModalOpen(true);
        } catch (err: any) {
            console.error('Error adding employee:', err);
            const errorMessage = err.response?.data?.message || 'Failed to add employee. Please try again.';
            setError(errorMessage);
        }
    };

    // Function to handle modal close and navigate to employee details
    const handleModalClose = () => {
        setIsModalOpen(false);
        navigate({ to: employeeDetailsRoute.to });
    };

    // Function to handle cancel button click
    const handleCancel = () => {
        navigate({ to: employeeDetailsRoute.to });
    };

    // Function to format employee data for display
    const formatEmployeeInfo = () => {
        if (!employeeData) return null;

        return (
            <div className="employee-summary p-3">
                <h3 className="text-lg font-semibold mb-3">Employee Information Summary</h3>

                <div className="grid grid-cols-2 gap-3">
                    <div className="col">
                        <h4 className="font-medium text-gray-700">Personal Information</h4>
                        <div className="field-group mt-2">
                            <p><span className="font-medium">Username:</span> {employeeData.username}</p>
                            <p><span className="font-medium">Name:</span> {employeeData.firstName} {employeeData.lastName}</p>
                            <p><span className="font-medium">Email:</span> {employeeData.email}</p>
                            <p><span className="font-medium">Phone:</span> {employeeData.phoneNumber}</p>
                        </div>
                    </div>

                    <div className="col">
                        <h4 className="font-medium text-gray-700">Employment Details</h4>
                        <div className="field-group mt-2">
                            <p><span className="font-medium">Position:</span> {employeeData.position}</p>
                            <p><span className="font-medium">Department:</span> {employeeData.department}</p>
                            <p><span className="font-medium">Role:</span> {employeeData.role}</p>
                            <p><span className="font-medium">Joining Date:</span> {formatDateValue(employeeData.joining_date)}</p>
                        </div>
                    </div>
                </div>

                <div className="mt-4 text-center">
                    <p className="text-green-600 font-medium">
                        Employee has been successfully added to the system!
                    </p>
                </div>
            </div>
        );
    };

    return (
        <div className="p-4">
            <Toast ref={toast} position="top-right" />
            <Card
                title="Add Employee"
                subtitle="Enter employee details"
                variant="elevated"
                padding="large"
                className="max-w-3xl mx-auto"
            >
                <DynamicForm
                    schema={employeeFormSchema}
                    onSubmit={handleSubmit}
                    className="mt-4"
                    buttonHandlers={{
                        cancel: handleCancel
                    }}
                />
                {error && <div className="p-error mt-3 text-center">{error}</div>}
            </Card>

            {/* Modal to display employee information */}
            <Modal
                visible={isModalOpen}
                onHide={handleModalClose}
                header="Employee Added Successfully"
                footerButtons={[
                    {
                        label: "Okay",
                        onClick: handleModalClose,
                        variant: "primary"
                    }
                ]}
            >
                {formatEmployeeInfo()}
            </Modal>
        </div>
    );
};

export default AddEmployee;