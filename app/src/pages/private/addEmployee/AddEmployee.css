@import url('../../../variables.css');

.employee-summary {
    background-color: var(--surface-card);
    border-radius: var(--border-radius);
    padding: 1rem;
}

.employee-summary h3 {
    color: var(--text-color);
    margin-bottom: 1rem;
}

.employee-summary h4 {
    color: var(--text-color-secondary);
    margin-bottom: 0.5rem;
}

.employee-summary .field-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.employee-summary .field-group p {
    margin: 0;
}

.employee-summary .field-group p span.font-medium {
    color: var(--text-color-secondary);
    margin-right: 0.5rem;
}

/* Responsive layout using Flexbox */
@media screen and (max-width: 768px) {
    .employee-summary .flex-wrap {
        flex-direction: column;
    }
}
