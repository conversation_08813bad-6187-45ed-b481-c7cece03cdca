@import url('../../../variables.css');

.location_details .card {
  background: var(--surface-card);
  border-radius: var(--border-radius);
  padding: var(--card-padding-screen);
  box-shadow: var(--card-shadow);
}

/* .p-dialog .p-dialog-footer {
  padding: 0;
} */

/* Status badges */
.status-badge {
  display: inline-flex;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-badge.primary {
  background-color: #e6f7e6;
  color: #2e7d32;
}

.status-badge.not-primary {
  background-color: #f5f5f5;
  color: #757575;
}

/* Card height consistency */
.location_details .h-full {
  height: 100%;
}

/* Action buttons */
.location_details .p-button.p-button-sm {
  padding: 0.25rem 0.5rem;
}

/* Primary button styling */
.primary-button-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Form modal */
.location_details .p-dialog-content {
  padding: var(--card-padding-screen);
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .location_details .card {
    padding: var(--card-padding-mobile);
  }

  .location_details .p-datatable .p-datatable-tbody > tr > td:last-child {
    text-align: center;
  }

  .location_details .p-dialog {
    width: 90vw !important;
  }

  .location_details .p-dialog-content {
    padding: var(--card-padding-mobile);
  }
}
