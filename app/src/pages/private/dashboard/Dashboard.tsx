import React from 'react';
import '@pages/private/dashboard/Dashboard.css';
import '@pages/private/dashboard/DashboardCompact.css';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Link } from '@tanstack/react-router';
import { ROUTES } from '@/constants/routes.constant';
import Card from '@/components/ui/Card/Card';
import Typography from '@/components/ui/Typography';
import Button from '@/components/ui/Button';
import { EmployeeRole, EmployeeStatus } from '@/types/employee';

// Interfaces for our static data
interface StatCard {
    title: string;
    value: string;
    icon: string;
    color: string;
    change?: string;
    trend?: 'up' | 'down' | 'neutral';
}

interface RecentEmployee {
    id: string;
    name: string;
    position: string;
    department: string;
    status: EmployeeStatus;
    joiningDate: string;
}

interface RecentProduct {
    id: string;
    name: string;
    category: string;
    price: number;
    sku: string;
}

interface QuickAccessCard {
    title: string;
    icon: string;
    description: string;
    route: string;
    color: string;
}

const Dashboard: React.FC = () => {
    // Stats cards data
    const statsCards: StatCard[] = [
        { title: 'Total Employees', value: '124', icon: 'pi pi-users', color: 'var(--primary-color)' },
        { title: 'Active Products', value: '287', icon: 'pi pi-shopping-bag', color: '#FF9800', change: '+12', trend: 'up' },
        { title: 'Departments', value: '8', icon: 'pi pi-sitemap', color: '#4CAF50' },
        { title: 'Pending Requests', value: '14', icon: 'pi pi-clock', color: '#E91E63', change: '+3', trend: 'up' }
    ];

    // Quick access cards
    const quickAccessCards: QuickAccessCard[] = [
        {
            title: 'Organization Catalog',
            icon: 'pi pi-shopping-bag',
            description: 'Manage organization items and products',
            route: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.ORGANIZATION_ITEM_CATALOG,
            color: '#3B82F6'
        },
        {
            title: 'Employee Directory',
            icon: 'pi pi-users',
            description: 'View and manage employees',
            route: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.EMPLOYEE_DETAILS,
            color: '#10B981'
        },
        {
            title: 'Departments',
            icon: 'pi pi-sitemap',
            description: 'Manage organizational departments',
            route: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.DEPARTMENT_DETAILS,
            color: '#F59E0B'
        },
        {
            title: 'Locations',
            icon: 'pi pi-map-marker',
            description: 'View and manage locations',
            route: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.LOCATION_DETAILS,
            color: '#8B5CF6'
        }
    ];

    // Recent employees data
    const recentEmployees: RecentEmployee[] = [
        {
            id: 'EMP001',
            name: 'John Doe',
            position: 'Software Engineer',
            department: 'Engineering',
            status: EmployeeStatus.ACTIVE,
            joiningDate: '2023-01-15'
        },
        {
            id: 'EMP002',
            name: 'Jane Smith',
            position: 'HR Manager',
            department: 'Human Resources',
            status: EmployeeStatus.ACTIVE,
            joiningDate: '2022-11-05'
        },
        {
            id: 'EMP003',
            name: 'Robert Johnson',
            position: 'Finance Analyst',
            department: 'Finance',
            status: EmployeeStatus.ON_LEAVE,
            joiningDate: '2023-03-20'
        },
        {
            id: 'EMP004',
            name: 'Emily Davis',
            position: 'Marketing Specialist',
            department: 'Marketing',
            status: EmployeeStatus.ACTIVE,
            joiningDate: '2023-02-10'
        },
        {
            id: 'EMP005',
            name: 'Michael Wilson',
            position: 'Operations Manager',
            department: 'Operations',
            status: EmployeeStatus.ACTIVE,
            joiningDate: '2022-09-15'
        }
    ];

    // Recent products data
    const recentProducts: RecentProduct[] = [
        { id: 'PRD001', name: 'Premium Ballpoint Pen Set', category: 'Office Supplies', price: 15.99, sku: 'PEN-BP-001' },
        { id: 'PRD002', name: 'Ergonomic Office Chair', category: 'Furniture', price: 249.99, sku: 'FRN-CHR-002' },
        { id: 'PRD003', name: 'Wireless Keyboard and Mouse', category: 'Electronics', price: 59.99, sku: 'ELC-KBM-003' },
        { id: 'PRD004', name: 'Desk Organizer Set', category: 'Office Supplies', price: 24.99, sku: 'ORG-DSK-004' },
        { id: 'PRD005', name: 'LED Desk Lamp', category: 'Lighting', price: 34.99, sku: 'LGT-DSK-005' }
    ];

    // Status badge template for employee data table
    const employeeStatusTemplate = (rowData: RecentEmployee) => {
        const getStatusClass = (status: EmployeeStatus) => {
            switch (status) {
                case EmployeeStatus.ACTIVE:
                    return 'status-badge active';
                case EmployeeStatus.ON_LEAVE:
                    return 'status-badge on-leave';
                case EmployeeStatus.INACTIVE:
                    return 'status-badge inactive';
                case EmployeeStatus.TERMINATED:
                    return 'status-badge terminated';
                default:
                    return 'status-badge';
            }
        };

        return <span className={getStatusClass(rowData.status)}>{rowData.status}</span>;
    };

    // Price template for product data table
    const priceTemplate = (rowData: RecentProduct) => {
        return <span>${rowData.price.toFixed(2)}</span>;
    };

    return (
        <div className="dashboard dashboard-compact">
            {/* Welcome Section */}
            <div className="welcome-section mb-4">
                <Typography variant="h4" weight="bold" className="mb-2">Welcome to Avinya Operations</Typography>
                <Typography variant="body1" color="secondary">Manage your organization's resources, employees, and inventory efficiently.</Typography>
            </div>

            {/* Stats Cards */}
            <div className="grid mb-4">
                {statsCards.map((card, index) => (
                    <div key={index} className="col-12 md:col-6 lg:col-3">
                        <Card variant="elevated" className="stat-card h-full">
                            <div className="flex align-items-center justify-content-between">
                                <div>
                                    <Typography variant="body2" color="secondary" className="mb-2">{card.title}</Typography>
                                    <div className="flex align-items-center">
                                        <Typography variant="h3" weight="bold" className="mb-0">{card.value}</Typography>
                                        {card.change && (
                                            <span className={`ml-2 trend-badge ${card.trend}`}>
                                                {card.trend === 'up' ? '↑' : '↓'} {card.change}
                                            </span>
                                        )}
                                    </div>
                                </div>
                                <div className="stat-icon" style={{ backgroundColor: card.color }}>
                                    <i className={card.icon}></i>
                                </div>
                            </div>
                        </Card>
                    </div>
                ))}
            </div>

            {/* Quick Access Cards */}
            <div className="grid mb-4">
                <div className="col-12">
                    <Typography variant="h5" weight="semibold" className="mb-3">Quick Access</Typography>
                </div>
                {quickAccessCards.map((card, index) => (
                    <div key={index} className="col-12 md:col-6 lg:col-3">
                        <Link to={card.route} className="text-decoration-none">
                            <Card variant="elevated" className="quick-access-card h-full hoverable">
                                <div className="flex flex-column align-items-center text-center">
                                    <div className="quick-access-icon mb-3" style={{ backgroundColor: card.color }}>
                                        <i className={card.icon}></i>
                                    </div>
                                    <Typography variant="h6" weight="semibold" className="mb-2">{card.title}</Typography>
                                    <Typography variant="body2" color="secondary">{card.description}</Typography>
                                </div>
                            </Card>
                        </Link>
                    </div>
                ))}
            </div>

            {/* Recent Employees and Products */}
            <div className="grid">
                {/* Recent Employees */}
                <div className="col-12 lg:col-6 mb-4 lg:mb-0">
                    <Card title="Recent Employees" variant="elevated" className="h-full">
                        <div className="flex justify-content-between mb-3">
                            <Typography variant="body2" color="secondary">Recently added employees</Typography>
                            <Link to={ROUTES.PRIVATE.APP + ROUTES.PRIVATE.EMPLOYEE_DETAILS}>
                                <Button variant="outline" size="small">
                                    View All
                                </Button>
                            </Link>
                        </div>
                        <DataTable value={recentEmployees} stripedRows showGridlines size="small">
                            <Column field="id" header="ID" style={{ width: '10%' }}></Column>
                            <Column field="name" header="Name" style={{ width: '25%' }}></Column>
                            <Column field="position" header="Position" style={{ width: '25%' }}></Column>
                            <Column field="department" header="Department" style={{ width: '20%' }}></Column>
                            <Column field="status" header="Status" body={employeeStatusTemplate} style={{ width: '20%' }}></Column>
                        </DataTable>
                    </Card>
                </div>

                {/* Recent Products */}
                <div className="col-12 lg:col-6">
                    <Card title="Recent Products" variant="elevated" className="h-full">
                        <div className="flex justify-content-between mb-3">
                            <Typography variant="body2" color="secondary">Recently added products</Typography>
                            <Link to={ROUTES.PRIVATE.APP + ROUTES.PRIVATE.ORGANIZATION_ITEM_CATALOG}>
                                <Button variant="outline" size="small">
                                    View All
                                </Button>
                            </Link>
                        </div>
                        <DataTable value={recentProducts} stripedRows showGridlines size="small">
                            <Column field="id" header="ID" style={{ width: '10%' }}></Column>
                            <Column field="name" header="Name" style={{ width: '35%' }}></Column>
                            <Column field="category" header="Category" style={{ width: '20%' }}></Column>
                            <Column field="price" header="Price" body={priceTemplate} style={{ width: '15%' }}></Column>
                            <Column field="sku" header="SKU" style={{ width: '20%' }}></Column>
                        </DataTable>
                    </Card>
                </div>
            </div>
        </div>
    );
};

export default Dashboard;