@import url('../../../variables.css');

.dashboard {
    padding: 1rem;
}

.dashboard .card {
    background: var(--surface-card);
    border-radius: var(--border-radius);
    padding: var(--card-padding-screen);
    box-shadow: var(--card-shadow);
}

/* Welcome Section */
.welcome-section {
    margin-bottom: 2rem;
}

/* Stat Cards */
.stat-card {
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.trend-badge {
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.125rem 0.375rem;
    border-radius: 4px;
}

.trend-badge.up {
    color: #4CAF50;
}

.trend-badge.down {
    color: #F44336;
}

/* Quick Access Cards */
.quick-access-card {
    transition: all 0.3s ease;
}

.quick-access-icon {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.75rem;
    margin-bottom: 1rem;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-badge.active {
    background-color: #e6f7e6;
    color: #2e7d32;
}

.status-badge.on-leave {
    background-color: #fff8e1;
    color: #f57c00;
}

.status-badge.inactive {
    background-color: #f5f5f5;
    color: #757575;
}

.status-badge.terminated {
    background-color: #ffebee;
    color: #c62828;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
    .dashboard .card {
        padding: var(--card-padding-mobile);
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1.25rem;
    }

    .quick-access-icon {
        width: 48px;
        height: 48px;
        font-size: 1.5rem;
    }
}