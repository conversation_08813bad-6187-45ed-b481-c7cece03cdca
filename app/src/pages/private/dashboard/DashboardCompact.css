/* Dashboard Compact Styles - Only applied to dashboard page */
.dashboard-compact .stat-card {
    transform: scale(0.95);
    transform-origin: center;
    margin-bottom: 0.5rem;
}

.dashboard-compact .quick-access-card {
    transform: scale(0.95);
    transform-origin: center;
    margin-bottom: 0.5rem;
}

.dashboard-compact .quick-access-icon {
    width: 56px;
    height: 56px;
    font-size: 1.5rem;
    margin-bottom: 0.75rem;
}

.dashboard-compact .stat-icon {
    width: 42px;
    height: 42px;
    font-size: 1.25rem;
}

.dashboard-compact .grid {
    row-gap: 0.75rem !important;
}

.dashboard-compact .grid > .col-12 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}

.dashboard-compact .mb-4 {
    margin-bottom: 1rem !important;
}

.dashboard-compact .welcome-section {
    margin-bottom: 1.5rem;
}

.dashboard-compact .p-datatable {
    font-size: 0.9rem;
}

.dashboard-compact .p-datatable .p-datatable-thead > tr > th {
    padding: 0.75rem 0.75rem;
}

.dashboard-compact .p-datatable .p-datatable-tbody > tr > td {
    padding: 0.5rem 0.75rem;
}

.dashboard-compact h3 {
    font-size: 1.75rem;
}

.dashboard-compact h4 {
    font-size: 1.5rem;
}

.dashboard-compact h5 {
    font-size: 1.25rem;
}

.dashboard-compact h6 {
    font-size: 1rem;
}

.dashboard-compact .card__title {
    font-size: 1.1rem;
}

.dashboard-compact .card__content {
    padding: 0.75rem 1rem !important;
}

.dashboard-compact .card__header {
    padding: 0.75rem 1rem 0.5rem !important;
}

/* Preserve hover effects */
.dashboard-compact .stat-card:hover,
.dashboard-compact .quick-access-card:hover {
    transform: translateY(-5px) scale(0.95);
}
