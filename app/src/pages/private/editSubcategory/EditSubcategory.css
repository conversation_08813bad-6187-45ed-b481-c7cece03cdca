.edit-subcategory .card {
  background: var(--surface-card);
  border-radius: var(--border-radius);
  padding: var(--card-padding-screen);
  box-shadow: var(--card-shadow);
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: var(--text-secondary);
  font-style: italic;
}

.subcategory-info {
  margin-top: 1rem;
}

.subcategory-info p {
  margin-bottom: 0.5rem;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .edit-subcategory .card {
    padding: var(--card-padding-mobile);
  }
}
