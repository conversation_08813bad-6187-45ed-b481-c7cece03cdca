.edit-category .card {
  background: var(--surface-card);
  border-radius: var(--border-radius);
  padding: var(--card-padding-screen);
  box-shadow: var(--card-shadow);
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: var(--text-secondary);
  font-style: italic;
}

.category-info {
  margin-top: 1rem;
}

.category-image-preview {
  margin-top: 1rem;
  max-width: 300px;
}

.category-image-preview img {
  width: 100%;
  height: auto;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .edit-category .card {
    padding: var(--card-padding-mobile);
  }
}
