import React, { useState, useRef, useEffect } from 'react';
import { useNavigate, useSearch } from '@tanstack/react-router';
import { addSubcategoryRoute } from '@/routes/private/addSubcategory.route';
import { DynamicForm } from '@/components/form/DynamicForm';
import Card from '@/components/ui/Card/Card';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';

import subcategoryFormSchemaJson from '@/formSchemas/subcategoryForm.json';
import type { FormSchema } from '@/components/form/DynamicForm';
import { useCategories, useCreateSubcategory } from '@/hooks/useCatalog';
import { CreateSubcategoryRequest } from '@/types/catalog.types';
import { organizationItemCatalogRoute } from '@/routes/private/organizationItemCatalog.route';
import './AddSubcategory.css';

const AddSubcategory: React.FC = () => {
  const navigate = useNavigate();
  const toast = useRef<ToastRef>(null);
  const { categoryId: categoryIdParam } = useSearch({ from: addSubcategoryRoute.id }) as { categoryId?: string};
  const categoryId = categoryIdParam ? parseInt(categoryIdParam) : null;

  // State for form schema
  const [subcategoryFormSchema, setSubcategoryFormSchema] = useState<FormSchema>(subcategoryFormSchemaJson as FormSchema);

  // Fetch categories
  const { data: categoriesData } = useCategories();
  const categories = categoriesData?.data || [];

  // Create subcategory mutation
  const createSubcategoryMutation = useCreateSubcategory();

  // Update form schema with categories
  useEffect(() => {
    const updatedSchema = { ...subcategoryFormSchema };

    // Update category options
    const categoryField = updatedSchema.fields.find(field => field.name === 'categoryId');
    if (categoryField) {
      categoryField.options = categories.map(category => ({
        label: category.name,
        value: category.id,
      }));
    }

    setSubcategoryFormSchema(updatedSchema);
  }, [categories]);

  // Handle form submission
  const handleSubmit = async (data: any) => {
    try {
      const subcategoryData: CreateSubcategoryRequest = {
        name: data.name,
        description: data.description,
        categoryId: typeof data.categoryId === 'string' ? parseInt(data.categoryId) : data.categoryId,
        organizationId: 40928446087168, // Use the test organization ID
        imageFile: data.imageFile || null
      };

      await createSubcategoryMutation.mutateAsync(subcategoryData);

      // Show success toast and navigate back
      toast.current?.showSuccess('Subcategory created successfully');
      navigate({ to: organizationItemCatalogRoute.to });
    } catch (error) {
      console.error('Error creating subcategory:', error);
      toast.current?.showError('Failed to create subcategory');
    }
  };

  // Handle cancel button
  const handleCancel = () => {
    navigate({ to: organizationItemCatalogRoute.to });
  };

  // Get default values for form
  const getDefaultValues = () => {
    return {
      categoryId: categoryId || null
    };
  };

  return (
    <div className="add-subcategory p-4">
      <Toast ref={toast} position="top-right" />

      <Card
        title="Add Subcategory"
        subtitle="Enter subcategory details"
        variant="elevated"
        padding="large"
        className="max-w-3xl mx-auto"
      >
        <DynamicForm
          schema={subcategoryFormSchema}
          onSubmit={handleSubmit}
          defaultValues={getDefaultValues()}
          className="mt-4"
          buttonHandlers={{
            cancel: handleCancel
          }}
        />
      </Card>
    </div>
  );
};

export default AddSubcategory;
