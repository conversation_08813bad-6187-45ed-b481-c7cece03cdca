@import url('../../../variables.css');

.department_details .card {
  background: var(--surface-card);
  border-radius: var(--border-radius);
  padding: var(--card-padding-screen);
  box-shadow: var(--card-shadow);
}

.department_details .description-cell {
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.department-form-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Responsive styles */
@media screen and (max-width: 768px) {
  .department_details .card {
    padding: var(--card-padding-mobile);
  }

  .department-form-container {
    gap: 1rem;
  }
}
