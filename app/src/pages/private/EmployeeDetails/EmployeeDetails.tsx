import { ColumnConfig, DataGrid } from "@/components/ui/DataGrid/DataGrid";
import { DataTableStateEvent } from "primereact/datatable";
import { useEffect, useState } from "react";
import '@pages/private/EmployeeDetails/EmployeeDetails.css';
import Button from "@/components/ui/Button/Button";
import Modal from "@/components/ui/Modal/Modal";
import Card from "@/components/ui/Card/Card";
import { useNavigate } from "@tanstack/react-router";
import { addEmployeeRoute } from "@/routes/private/addEmployee.route";
import { editEmployeeRoute } from "@/routes/private/editEmployee.route";

interface User {
    id: number;
    name: string;
    email: string;
    role: string;
    status: string;
    lastLogin: string;
}

const EmployeeDetails: React.FC = () => {
    const [users, setUsers] = useState<User[]>([]);
    const [totalRecords, setTotalRecords] = useState(0);
    const [loading, setLoading] = useState(false);
    const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [currentUser, setCurrentUser] = useState<User | null>(null);
    const navigate = useNavigate();

    // Sample data - replace with actual API call
    const fetchUsers = async (first: number, rows: number) => {
        setLoading(true);
        try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1000));

            const mockData: User[] = Array.from({ length: 100 }, (_, index) => ({
                id: index + 1,
                name: `User ${index + 1}`,
                email: `user${index + 1}@example.com`,
                role: index % 3 === 0 ? 'Admin' : index % 3 === 1 ? 'Editor' : 'Viewer',
                status: index % 2 === 0 ? 'Active' : 'Inactive',
                lastLogin: new Date(Date.now() - Math.random() * 10000000000).toLocaleString()
            }));

            setUsers(mockData.slice(first, first + rows));
            setTotalRecords(mockData.length);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchUsers(0, 10);
    }, []);

    const handlePageChange = (event: { first: number; rows: number; page: number }) => {
        fetchUsers(event.first, event.rows);
    };

    const handleSort = (event: DataTableStateEvent) => {
        console.log('Sort field:', event.sortField);
        console.log('Sort order:', event.sortOrder);
        // Implement sorting logic here
    };

    // Handlers for add, edit, delete
    const handleAdd = () => {
        navigate({ to: addEmployeeRoute.to });
    };

    const handleEdit = (user: User) => {
        // Navigate to the edit employee page
        // In a real implementation, you would pass the employee ID as a parameter
        console.log(`Editing user with ID: ${user.id}`);
        navigate({ to: editEmployeeRoute.to });
    };

    const handleDelete = () => {
        if (selectedUsers.length === 0) return;
        // TODO: Implement delete logic (e.g., show confirmation and remove user)
        alert(`Delete users: ${selectedUsers.map(user => user.name).join(', ')} (not implemented)`);
    };

    const columns: ColumnConfig[] = [
        {
            field: 'name',
            header: 'Name',
            sortable: true
        },
        {
            field: 'email',
            header: 'Email',
            sortable: true
        },
        {
            field: 'role',
            header: 'Role',
            sortable: true
        },
        {
            field: 'status',
            header: 'Status',
            sortable: true,
            body: (rowData: User) => (
                <span className={`status-badge ${rowData.status.toLowerCase()}`}>
                    {rowData.status}
                </span>
            )
        },
        {
            field: 'lastLogin',
            header: 'Last Login',
            sortable: true
        },
        {
            field: 'actions',
            header: 'Actions',
            sortable: false,
            body: (rowData: User) => (
                <div className="flex gap-2 justify-content-center">
                    <Button
                        icon="pi pi-pencil"
                        variant="outline"
                        size="small"
                        onClick={() => handleEdit(rowData)}
                        aria-label="Edit"
                    />
                </div>
            )
        }
    ];

    return (
        <div className="employee_details p-4">
            <Card title="Employee Details" variant="elevated" className="mb-4">
                <div className="flex justify-content-end gap-2 mb-3">
                    <Button variant="primary" size="small" onClick={handleAdd} disabled={selectedUsers.length > 0}>
                        Add
                    </Button>
                    <Button variant="danger" size="small" onClick={handleDelete} disabled={selectedUsers.length === 0}>
                        Delete
                    </Button>
                    {/* TODO: move the below configs out of the componenent */}
                    <Modal
                        visible={isModalOpen}
                        onHide={() => {
                            setIsModalOpen(false);
                            setCurrentUser(null);
                        }}
                        header="User Details"
                        children={
                            <>
                                {currentUser && (
                                    <div className="flex flex-column gap-3 p-fluid">
                                        <div className="flex flex-column gap-2">
                                            <label className="font-bold">Name</label>
                                            <div>{currentUser.name}</div>
                                        </div>
                                        <div className="flex flex-column gap-2">
                                            <label className="font-bold">Email</label>
                                            <div>{currentUser.email}</div>
                                        </div>
                                        <div className="flex flex-column gap-2">
                                            <label className="font-bold">Role</label>
                                            <div>{currentUser.role}</div>
                                        </div>
                                        <div className="flex flex-column gap-2">
                                            <label className="font-bold">Status</label>
                                            <div>
                                                <span className={`status-badge ${currentUser.status.toLowerCase()}`}>
                                                    {currentUser.status}
                                                </span>
                                            </div>
                                        </div>
                                        <div className="flex flex-column gap-2">
                                            <label className="font-bold">Last Login</label>
                                            <div>{currentUser.lastLogin}</div>
                                        </div>
                                    </div>
                                )}
                            </>
                        }
                        footerButtons={[
                            {
                                label: 'Close',
                                icon: 'pi pi-times',
                                onClick: () => {
                                    setIsModalOpen(false);
                                    setCurrentUser(null);
                                },
                                variant: 'outline'
                            }
                        ]} />
                </div>
                <DataGrid
                    value={users}
                    columns={columns}
                    totalRecords={totalRecords}
                    loading={loading}
                    onPage={handlePageChange}
                    onSort={handleSort}
                    rows={10}
                    rowsPerPageOptions={[10, 25, 50]}
                    showGridLines={true}
                    stripedRows={true}
                    selection={selectedUsers}
                    selectionMode="checkbox"
                    onSelectionChange={e => setSelectedUsers(Array.isArray(e.value) ? e.value : [e.value])}
                />
            </Card>
        </div>
    );
}

export default EmployeeDetails;