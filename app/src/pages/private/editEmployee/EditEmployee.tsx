import React, { useState, useRef, useMemo } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { DynamicForm } from '@/components/form/DynamicForm';
import employeeFormSchemaJson from '@/formSchemas/employeeForm.json';
import type { FormSchema } from '@/components/form/DynamicForm';
import { useUpdateEmployee } from '@/hooks/useEmployee';
import { Employee, UpdateEmployeeRequest, EmployeeRole, EmployeeStatus } from '@/types/employee';
import Card from '@/components/ui/Card/Card';
import { employeeDetailsRoute } from '@/routes/private/employeeDetails.route';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import Modal from '@/components/ui/Modal/Modal';
import { getEnumValues } from '@/utils/enumUtils';
import './EditEmployee.css';

// Helper function to safely format date values
const formatDateValue = (dateValue: any): string => {
    if (!dateValue) return '';

    if (dateValue instanceof Date) {
        return dateValue.toLocaleDateString();
    }

    if (typeof dateValue === 'string') {
        // If it's already a string, return it
        return dateValue;
    }

    // Fallback: convert to string
    return String(dateValue);
};

// Mock employee data for development purposes
const mockEmployee: Employee = {
    id: '1',
    employee_id: 'EMP001',
    position: 'Software Engineer',
    department: 'Engineering',
    department_id: 'DEP001',
    role: EmployeeRole.SPECIALIST,
    status: EmployeeStatus.ACTIVE,
    joining_date: '2023-01-15',
    organization_id: 'ORG001',
    user_id: 'USR001',
    created_by_ip: '***********',
    created_by_user: 'admin',
    created_date: '2023-01-15T10:00:00Z',
    last_modified_by_ip: '***********',
    last_modified_by_user: 'admin',
    last_modified_date: '2023-01-15T10:00:00Z',
    version: 1,
    username: 'johndoe',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phoneNumber: '1234567890'
};

const EditEmployee: React.FC = () => {
    const navigate = useNavigate();
    const toast = useRef<ToastRef>(null);
    const [error, setError] = useState<string | null>(null);
    const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
    const [employeeData, setEmployeeData] = useState<UpdateEmployeeRequest | null>(null);

    // In a real implementation, you would get the employee ID from the route params
    // and fetch the employee data using the useEmployee hook
    const employeeId = '1'; // Mock ID for development
    const updateEmployeeMutation = useUpdateEmployee(employeeId);

    // Get enum values
    const employeeRoleValues = useMemo(() => getEnumValues(EmployeeRole), []);
    const employeeStatusValues = useMemo(() => getEnumValues(EmployeeStatus), []);

    // Create a dynamic form schema with the enum values and modified submit button
    const employeeFormSchema = useMemo(() => {
        // Clone the original schema
        const schema = JSON.parse(JSON.stringify(employeeFormSchemaJson)) as FormSchema;

        // Find the role field and update its options
        const roleField = schema.fields.find(field => field.name === 'role');
        if (roleField) {
            roleField.options = employeeRoleValues;
        }

        // Find the status field and update its options
        const statusField = schema.fields.find(field => field.name === 'status');
        if (statusField) {
            statusField.options = employeeStatusValues;
        }

        // Update the submit button label
        if (schema.actions && schema.actions.length > 0) {
            schema.actions[0].label = 'Save Changes';
        }

        return schema;
    }, [employeeRoleValues, employeeStatusValues]);

    // Prepare default values for the form from the mock employee data
    const defaultValues = useMemo(() => {
        return {
            username: mockEmployee.username,
            firstName: mockEmployee.firstName,
            lastName: mockEmployee.lastName,
            email: mockEmployee.email,
            'phone-number': mockEmployee.phoneNumber,
            position: mockEmployee.position,
            department: mockEmployee.department,
            department_id: mockEmployee.department_id,
            role: mockEmployee.role,
            status: mockEmployee.status,
            joining_date: mockEmployee.joining_date,
            organization_id: mockEmployee.organization_id
        };
    }, []);

    const handleSubmit = async (data: any) => {
        try {
            setError(null);

            // Format the data to match our UpdateEmployeeRequest type
            const formattedData: UpdateEmployeeRequest = {
                // User information
                firstName: data.firstName,
                lastName: data.lastName,
                email: data.email,
                phoneNumber: data['phone-number'], // Note the different naming in the form schema

                // Employee information
                position: data.position,
                department: data.department,
                department_id: data.department_id,
                role: data.role as EmployeeRole,
                status: data.status as EmployeeStatus,
                joining_date: data.joining_date ? formatDateValue(data.joining_date) : undefined,
                organization_id: data.organization_id
            };

            // For development, just log the data instead of making the API call
            console.log('Employee update data:', formattedData);
            // await updateEmployeeMutation.mutateAsync(formattedData);

            // Show success message
            toast.current?.showSuccess('Employee information successfully updated');

            // Store employee data for display in modal
            setEmployeeData(formattedData);

            // Show modal with employee information
            setIsModalOpen(true);
        } catch (err: any) {
            console.error('Error updating employee:', err);
            const errorMessage = err.response?.data?.message || 'Failed to update employee. Please try again.';
            setError(errorMessage);
        }
    };

    // Function to handle modal close and navigate to employee details
    const handleModalClose = () => {
        setIsModalOpen(false);
        navigate({ to: employeeDetailsRoute.to });
    };

    // Function to handle cancel button click
    const handleCancel = () => {
        navigate({ to: employeeDetailsRoute.to });
    };

    // Function to format employee data for display
    const formatEmployeeInfo = () => {
        if (!employeeData) return null;

        return (
            <div className="employee-summary p-3">
                <h3 className="text-lg font-semibold mb-3">Updated Employee Information</h3>

                <div className="flex flex-wrap gap-3">
                    <div className="flex-1 min-w-[250px]">
                        <h4 className="font-medium text-gray-700">Personal Information</h4>
                        <div className="field-group mt-2">
                            <p><span className="font-medium">Name:</span> {employeeData.firstName} {employeeData.lastName}</p>
                            <p><span className="font-medium">Email:</span> {employeeData.email}</p>
                            <p><span className="font-medium">Phone:</span> {employeeData.phoneNumber}</p>
                        </div>
                    </div>

                    <div className="flex-1 min-w-[250px]">
                        <h4 className="font-medium text-gray-700">Employment Details</h4>
                        <div className="field-group mt-2">
                            <p><span className="font-medium">Position:</span> {employeeData.position}</p>
                            <p><span className="font-medium">Department:</span> {employeeData.department}</p>
                            <p><span className="font-medium">Role:</span> {employeeData.role}</p>
                            <p><span className="font-medium">Status:</span> {employeeData.status}</p>
                            <p><span className="font-medium">Joining Date:</span> {formatDateValue(employeeData.joining_date)}</p>
                        </div>
                    </div>
                </div>

                <div className="mt-4 text-center">
                    <p className="text-green-600 font-medium">
                        Employee information has been successfully updated!
                    </p>
                </div>
            </div>
        );
    };

    return (
        <div className="p-4">
            <Toast ref={toast} position="top-right" />
            <Card
                title="Edit Employee"
                subtitle="Update employee details"
                variant="elevated"
                padding="large"
                className="max-w-3xl mx-auto"
            >
                <DynamicForm
                    schema={employeeFormSchema}
                    onSubmit={handleSubmit}
                    defaultValues={defaultValues}
                    className="mt-4"
                    buttonHandlers={{
                        cancel: handleCancel
                    }}
                />
                {error && <div className="p-error mt-3 text-center">{error}</div>}
            </Card>

            {/* Modal to display employee information */}
            <Modal
                visible={isModalOpen}
                onHide={handleModalClose}
                header="Employee Updated Successfully"
                footerButtons={[
                    {
                        label: "Okay",
                        onClick: handleModalClose,
                        variant: "primary"
                    }
                ]}
            >
                {formatEmployeeInfo()}
            </Modal>
        </div>
    );
};

export default EditEmployee;
