@import url('../../../variables.css');

.organization_details .card {
    background: var(--surface-card);
    border-radius: var(--border-radius);
    padding: var(--card-padding-screen);
    box-shadow: var(--card-shadow);
}

/* Status badges */
.status-badge {
    display: inline-flex;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-badge.active {
    background-color: #e6f7e6;
    color: #2e7d32;
}

.status-badge.inactive {
    background-color: #ffebee;
    color: #c62828;
}

/* Card height consistency */
.organization_details .h-full {
    height: 100%;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
    .organization_details .card {
        padding: var(--card-padding-mobile);
    }
}