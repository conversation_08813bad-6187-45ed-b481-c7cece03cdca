import React, { useState } from 'react';
import '@pages/private/organizationDetails/OrganizationDetails.css';
import Card from '@/components/ui/Card/Card';
import Button from '@/components/ui/Button/Button';
import Modal from '@/components/ui/Modal/Modal';
import { InputText } from 'primereact/inputtext';
import { OrganizationDetailResponse } from '@/types/organization.types';
import { useCurrentOrganization, useCurrentOrganizationPrimaryLocation } from '@/hooks/useOrganization';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Message } from 'primereact/message';

const OrganizationDetails: React.FC = () => {
    // Fetch organization data from API
    const { data: organization, isLoading, error, isError } = useCurrentOrganization();

    // Fetch primary location data separately
    const {
        data: primaryLocation,
        isLoading: isPrimaryLocationLoading,
        error: primaryLocationError
    } = useCurrentOrganizationPrimaryLocation();

    const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
    const [logoFileId, setLogoFileId] = useState<string>('');

    const handleEdit = () => {
        setLogoFileId(organization?.logoFileId || '');
        setIsModalOpen(true);
    };

    const handleSave = () => {
        // TODO: Implement organization update functionality
        // This would use the useUpdateOrganization hook
        console.log('Save organization with logoFileId:', logoFileId);
        setIsModalOpen(false);
    };

    // Loading state
    if (isLoading) {
        return (
            <div className="organization_details p-4">
                <Card title="Organization Details" variant="elevated" className="mb-4">
                    <div className="flex justify-content-center align-items-center" style={{ minHeight: '200px' }}>
                        <ProgressSpinner />
                    </div>
                </Card>
            </div>
        );
    }

    // Error state
    if (isError || !organization) {
        return (
            <div className="organization_details p-4">
                <Card title="Organization Details" variant="elevated" className="mb-4">
                    <Message
                        severity="error"
                        text={error?.message || "Failed to load organization details"}
                        className="w-full"
                    />
                </Card>
            </div>
        );
    }

    return (
        <div className="organization_details p-4">
            <Card title="Organization Details" variant="elevated" className="mb-4">
                <div className="grid">
                    {/* Organization Basic Info */}
                    <div className="col-12 md:col-6 lg:col-4 mb-4">
                        <Card title="Basic Information" variant="outlined" className="h-full">
                            <div className="flex flex-column gap-3">
                                <div className="flex flex-column gap-1">
                                    <label className="text-sm font-semibold">Name</label>
                                    <div>{organization.name}</div>
                                </div>
                                <div className="flex flex-column gap-1">
                                    <label className="text-sm font-semibold">Description</label>
                                    <div>{organization.description}</div>
                                </div>
                                <div className="flex flex-column gap-1">
                                    <label className="text-sm font-semibold">Logo File ID</label>
                                    <div className="flex align-items-center">
                                        <span>{organization.logoFileId || 'Not provided'}</span>
                                        <Button
                                            variant="outline"
                                            size="small"
                                            onClick={handleEdit}
                                            icon="pi pi-pencil"
                                            className="ml-2"
                                        />
                                    </div>
                                </div>
                            </div>
                        </Card>
                    </div>

                    {/* Primary Location Information */}
                    <div className="col-12 md:col-6 lg:col-4 mb-4">
                        <Card title="Primary Location" variant="outlined" className="h-full">
                            {isPrimaryLocationLoading ? (
                                <div className="flex justify-content-center align-items-center" style={{ minHeight: '100px' }}>
                                    <ProgressSpinner size="small" />
                                </div>
                            ) : primaryLocationError ? (
                                <div className="flex align-items-center justify-content-center" style={{ minHeight: '100px' }}>
                                    <span className="text-500">Primary location not set</span>
                                </div>
                            ) : primaryLocation ? (
                                <div className="flex flex-column gap-3">
                                    <div className="flex flex-column gap-1">
                                        <label className="text-sm font-semibold">Location Name</label>
                                        <div>{primaryLocation.name}</div>
                                    </div>
                                    <div className="flex flex-column gap-1">
                                        <label className="text-sm font-semibold">Timezone</label>
                                        <div>{primaryLocation.timezone}</div>
                                    </div>
                                    <div className="flex flex-column gap-1">
                                        <label className="text-sm font-semibold">Address Line 1</label>
                                        <div>{primaryLocation.address.addressLine1}</div>
                                    </div>
                                    {primaryLocation.address.addressLine2 && (
                                        <div className="flex flex-column gap-1">
                                            <label className="text-sm font-semibold">Address Line 2</label>
                                            <div>{primaryLocation.address.addressLine2}</div>
                                        </div>
                                    )}
                                    <div className="flex flex-column gap-1">
                                        <label className="text-sm font-semibold">City</label>
                                        <div>{primaryLocation.address.city}</div>
                                    </div>
                                    <div className="flex flex-column gap-1">
                                        <label className="text-sm font-semibold">State</label>
                                        <div>{primaryLocation.address.state}</div>
                                    </div>
                                    <div className="flex flex-column gap-1">
                                        <label className="text-sm font-semibold">Country</label>
                                        <div>{primaryLocation.address.countryCode}</div>
                                    </div>
                                    {primaryLocation.address.postalCode && (
                                        <div className="flex flex-column gap-1">
                                            <label className="text-sm font-semibold">Postal Code</label>
                                            <div>{primaryLocation.address.postalCode}</div>
                                        </div>
                                    )}
                                    {primaryLocation.address.latitude && primaryLocation.address.longitude && (
                                        <div className="flex flex-column gap-1">
                                            <label className="text-sm font-semibold">Coordinates</label>
                                            <div>{primaryLocation.address.latitude}, {primaryLocation.address.longitude}</div>
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <div className="flex align-items-center justify-content-center" style={{ minHeight: '100px' }}>
                                    <span className="text-500">Primary location not set</span>
                                </div>
                            )}
                        </Card>
                    </div>

                    {/* Organization System Information */}
                    <div className="col-12 md:col-6 lg:col-4 mb-4">
                        <Card title="System Information" variant="outlined" className="h-full">
                            <div className="flex flex-column gap-3">
                                <div className="flex flex-column gap-1">
                                    <label className="text-sm font-semibold">Organization ID</label>
                                    <div>{organization.id}</div>
                                </div>
                                <div className="flex flex-column gap-1">
                                    <label className="text-sm font-semibold">Status</label>
                                    <div>
                                        <span className={`status-badge ${organization.enabled ? 'active' : 'inactive'}`}>
                                            {organization.enabled ? 'Enabled' : 'Disabled'}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </Card>
                    </div>
                </div>

                {/* Edit Logo File ID Modal */}
                <Modal
                    visible={isModalOpen}
                    onHide={() => setIsModalOpen(false)}
                    header="Edit Logo File ID"
                    children={
                        <div className="flex flex-column gap-3 p-fluid">
                            <div className="field">
                                <label htmlFor="logo_file_id" className="font-bold">Logo File ID</label>
                                <InputText
                                    id="logo_file_id"
                                    value={logoFileId}
                                    onChange={(e) => setLogoFileId(e.target.value)}
                                    placeholder="Enter Logo File ID"
                                />
                            </div>
                        </div>
                    }
                    footerButtons={[
                        {
                            label: 'Save',
                            icon: 'pi pi-check',
                            onClick: handleSave
                        },
                        {
                            label: 'Cancel',
                            icon: 'pi pi-times',
                            onClick: () => setIsModalOpen(false),
                            variant: 'outline'
                        }
                    ]}
                />
            </Card>
        </div>
    );
};

export default OrganizationDetails;