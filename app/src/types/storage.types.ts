/**
 * Types for object storage operations
 */

/**
 * Response from a file upload operation
 */
export interface FileUploadResponse {
  /**
   * The key/path of the uploaded file in the storage bucket
   */
  key: string;
  
  /**
   * The original filename
   */
  filename: string;
  
  /**
   * The size of the file in bytes
   */
  size: number;
  
  /**
   * The MIME type of the file
   */
  contentType: string;
  
  /**
   * The URL to access the file (may be temporary)
   */
  url?: string;
}

/**
 * Response from a batch file upload operation
 */
export interface BatchFileUploadResponse {
  /**
   * Array of successfully uploaded files
   */
  successful: FileUploadResponse[];
  
  /**
   * Array of files that failed to upload
   */
  failed: {
    /**
     * The original filename
     */
    filename: string;
    
    /**
     * The error message
     */
    error: string;
  }[];
}

/**
 * Request to generate a presigned URL for a file
 */
export interface GetFileUrlRequest {
  /**
   * The key/path of the file in the storage bucket
   */
  key: string;
  
  /**
   * The expiration time in seconds (optional, server may use default)
   */
  expiresIn?: number;
}

/**
 * Response from a presigned URL generation request
 */
export interface GetFileUrlResponse {
  /**
   * The presigned URL that can be used to access the file
   */
  url: string;
  
  /**
   * The key/path of the file in the storage bucket
   */
  key: string;
  
  /**
   * When the URL will expire (ISO date string)
   */
  expiresAt: string;
}

/**
 * Options for file uploads
 */
export interface FileUploadOptions {
  /**
   * The bucket to upload to (optional, server may use default)
   */
  bucket?: string;
  
  /**
   * The folder path within the bucket (optional)
   */
  folder?: string;
  
  /**
   * Whether to make the file publicly accessible (optional)
   */
  public?: boolean;
  
  /**
   * Custom metadata to attach to the file (optional)
   */
  metadata?: Record<string, string>;
}
