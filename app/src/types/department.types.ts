/**
 * Department interface and related types for the department management system
 */

/**
 * Department DTO matching backend DepartmentDTO structure
 */
export interface DepartmentDTO {
  id?: number; // Optional for create requests
  name: string;
  description?: string;
  organizationId: number;
}

/**
 * Legacy Department interface for backward compatibility
 * @deprecated Use DepartmentDTO instead
 */
export interface Department {
  id: string;
  name: string;
  description?: string | null;
  organization_id: string;

  // Audit fields
  created_by_ip: string;
  created_by_user: string;
  created_date: string;
  last_modified_by_ip: string;
  last_modified_by_user: string;
  last_modified_date: string;
  version: number;
  active: boolean;
}

/**
 * Request object for creating a new department (matches backend DepartmentDTO)
 */
export interface CreateDepartmentRequest {
  name: string;
  description?: string;
  organizationId: number;
}

/**
 * Request object for updating a department (matches backend DepartmentDTO)
 */
export interface UpdateDepartmentRequest {
  name?: string;
  description?: string;
  organizationId?: number;
}

/**
 * Legacy request interfaces for backward compatibility
 * @deprecated Use CreateDepartmentRequest/UpdateDepartmentRequest with new structure
 */
export interface LegacyCreateDepartmentRequest {
  name: string;
  description?: string;
  organization_id?: string; // Optional, can be determined by the system
}

export interface LegacyUpdateDepartmentRequest {
  name?: string;
  description?: string;
  organization_id?: string;
}

/**
 * Request object for updating an existing department
 */
export interface UpdateDepartmentRequest {
  name?: string;
  description?: string;
}
