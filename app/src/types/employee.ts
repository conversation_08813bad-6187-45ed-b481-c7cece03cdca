/**
 * Employee interface and related types for the employee management system
 */

/**
 * Possible role values for an employee
 */
export enum EmployeeRole {
  MANAGER = 'MANAGER',
  SUPERVISOR = 'SUPERVISOR',
  TEAM_LEAD = 'TEAM_LEAD',
  SPECIALIST = 'SPECIALIST',
  ASSOCIATE = 'ASSOCIATE',
  INTERN = 'INTERN',
}

/**
 * Represents an employee in the system, aligned with the PostgreSQL database schema
 */
export interface Employee {
  id: string;
  employee_id: string;
  position: string;
  department: string;
  department_id: string;
  role: EmployeeRole;
  status: EmployeeStatus;
  joining_date: string;
  termination_date?: string;
  organization_id: string;
  user_id: string;

  // Audit fields
  created_by_ip: string;
  created_by_user: string;
  created_date: string;
  last_modified_by_ip: string;
  last_modified_by_user: string;
  last_modified_date: string;
  version: number;

  // Additional fields from user entity (not in employees table directly)
  username?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
}

/**
 * Possible status values for an employee
 */
export enum EmployeeStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  ON_LEAVE = 'ON_LEAVE',
  TERMINATED = 'TERMINATED',
}

/**
 * Request object for creating a new employee
 */
export interface CreateEmployeeRequest {
  // User information
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;

  // Employee information
  position: string;
  department: string;
  department_id?: string;
  role: EmployeeRole;
  joining_date?: string; // Optional, can be set by the system
  organization_id?: string; // Optional, can be determined by the system
}

/**
 * Request object for updating an existing employee
 */
export interface UpdateEmployeeRequest {
  // User information
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;

  // Employee information
  position?: string;
  department?: string;
  department_id?: string;
  role?: EmployeeRole;
  status?: EmployeeStatus;
  joining_date?: string;
  termination_date?: string;
  organization_id?: string;
}

/**
 * Response object for employee operations
 */
export interface EmployeeResponse {
  employee: Employee;
  message: string;
}
