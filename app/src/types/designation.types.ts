/**
 * Designation interface and related types for the designation management system
 */

/**
 * Designation DTO matching backend DesignationDTO structure
 */
export interface DesignationDTO {
  id?: number; // Optional for create requests
  name: string;
  description?: string;
  organizationId: number;
}

/**
 * Legacy Designation interface for backward compatibility
 * @deprecated Use DesignationDTO instead
 */
export interface Designation {
  id: string;
  name: string;
  description?: string | null;
  organization_id: string;

  // Audit fields
  created_by_ip: string;
  created_by_user: string;
  created_date: string;
  last_modified_by_ip: string;
  last_modified_by_user: string;
  last_modified_date: string;
  version: number;
  active: boolean;
}

/**
 * Request object for creating a new designation (matches backend DesignationDTO)
 */
export interface CreateDesignationRequest {
  name: string;
  description?: string;
  organizationId: number;
}

/**
 * Request object for updating an existing designation (matches backend DesignationDTO)
 */
export interface UpdateDesignationRequest {
  name?: string;
  description?: string;
  organizationId?: number;
}

/**
 * Legacy request interfaces for backward compatibility
 * @deprecated Use CreateDesignationRequest/UpdateDesignationRequest with new structure
 */
export interface LegacyCreateDesignationRequest {
  name: string;
  description?: string;
  organization_id?: string; // Optional, can be determined by the system
}

export interface LegacyUpdateDesignationRequest {
  name?: string;
  description?: string;
  organization_id?: string;
  active?: boolean;
}
