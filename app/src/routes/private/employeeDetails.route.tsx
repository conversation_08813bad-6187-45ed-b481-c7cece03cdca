import { createRoute } from "@tanstack/react-router";
import { ROUTES } from "@/constants/routes.constant";
import { appRoute } from "@/routes/private/app.route";
import EmployeeDetails from "@/pages/private/EmployeeDetails/EmployeeDetails";

export const employeeDetailsRoute = createRoute({
    getParentRoute: () => appRoute,
    path: ROUTES.PRIVATE.EMPLOYEE_DETAILS,
    component: EmployeeDetails,
})