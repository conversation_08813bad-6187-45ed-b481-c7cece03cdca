import { createRoute } from "@tanstack/react-router";
import { ROUTES } from "@/constants/routes.constant";
import { appRoute } from "@/routes/private/app.route";
import DepartmentDetails from "@/pages/private/departmentDetails/DepartmentDetails";

export const departmentDetailsRoute = createRoute({
  getParentRoute: () => appRoute,
  path: ROUTES.PRIVATE.DEPARTMENT_DETAILS,
  component: DepartmentDetails,
});
