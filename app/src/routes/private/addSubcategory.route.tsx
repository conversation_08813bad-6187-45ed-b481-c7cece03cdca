import { createRoute } from "@tanstack/react-router";
import { ROUTES } from "@/constants/routes.constant";
import { appRoute } from "@/routes/private/app.route";
import AddSubcategory from "@/pages/private/addSubcategory/AddSubcategory";

export const addSubcategoryRoute = createRoute({
  getParentRoute: () => appRoute,
  path: ROUTES.PRIVATE.ORGANIZATION_ITEM_CATALOG + ROUTES.PRIVATE.ADD_SUBCATEGORY,
  component: AddSubcategory,
});
