import { createRoute } from "@tanstack/react-router";
import { ROUTES } from "@/constants/routes.constant";
import { appRoute } from "@/routes/private/app.route";
import OrganizationDetails from "@/pages/private/organizationDetails/OrganizationDetails";

export const organizationDetailsRoute = createRoute({
    getParentRoute: () => appRoute,
    path: ROUTES.PRIVATE.ORGANIZATION_DETAILS,
    component: OrganizationDetails,
})