import { createRoute } from "@tanstack/react-router";
import { ROUTES } from "@/constants/routes.constant";
import { appRoute } from "@/routes/private/app.route";
import AddProduct from "@/pages/private/addProduct/AddProduct";

export const addProductRoute = createRoute({
  getParentRoute: () => appRoute,
  path: ROUTES.PRIVATE.ORGANIZATION_ITEM_CATALOG + ROUTES.PRIVATE.ADD_PRODUCT,
  component: AddProduct,
});
