import { createRoute } from "@tanstack/react-router";
import { ROUTES } from "@/constants/routes.constant";
import { appRoute } from "@/routes/private/app.route";
import EditSubcategory from "@/pages/private/editSubcategory/EditSubcategory";

export const editSubcategoryRoute = createRoute({
  getParentRoute: () => appRoute,
  path: ROUTES.PRIVATE.ORGANIZATION_ITEM_CATALOG + ROUTES.PRIVATE.EDIT_SUBCATEGORY,
  component: EditSubcategory,
  validateSearch: (search: Record<string, unknown>) => {
    // Ensure id is always a string and is required
    if (typeof search.id !== 'string' || !search.id) {
      throw new Error('Subcategory ID is required and must be a string');
    }
    return {
      id: search.id,
    };
  },
});
