import { createRoute } from "@tanstack/react-router";
import { ROUTES } from "@/constants/routes.constant";
import { appRoute } from "@/routes/private/app.route";
import AddCategory from "@/pages/private/addCategory/AddCategory";

export const addCategoryRoute = createRoute({
  getParentRoute: () => appRoute,
  path: ROUTES.PRIVATE.ORGANIZATION_ITEM_CATALOG + ROUTES.PRIVATE.ADD_CATEGORY,
  component: AddCategory,
});
