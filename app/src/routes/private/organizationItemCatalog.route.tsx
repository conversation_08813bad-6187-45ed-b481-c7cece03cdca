import { createRoute } from "@tanstack/react-router";
import { ROUTES } from "@/constants/routes.constant";
import { appRoute } from "@/routes/private/app.route";
import OrganizationItemCatalog from "@/pages/private/organizationItemCatalog/OrganizationItemCatalog";

export const organizationItemCatalogRoute = createRoute({
  getParentRoute: () => appRoute,
  path: ROUTES.PRIVATE.ORGANIZATION_ITEM_CATALOG,
  component: OrganizationItemCatalog,
});
