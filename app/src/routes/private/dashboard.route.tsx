import { createRoute } from "@tanstack/react-router";
import { ROUTES } from "@/constants/routes.constant";
import Dashboard from "@/pages/private/dashboard/Dashboard";
import { appRoute } from "@/routes/private/app.route";
import { DOCUMENT_HEADER_PREFIX } from "@/constants/app.constant";

export const dashboardRoute = createRoute({
    getParentRoute: () => appRoute,
    path: ROUTES.PRIVATE.DASHBOARD,
    component: Dashboard,
    head: () => {
        return {
            meta: [
                {
                    title: `${DOCUMENT_HEADER_PREFIX} - Dashboard`,
                },
            ],
        };
    },
})