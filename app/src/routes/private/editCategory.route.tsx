import { createRoute } from "@tanstack/react-router";
import { ROUTES } from "@/constants/routes.constant";
import { appRoute } from "@/routes/private/app.route";
import EditCategory from "@/pages/private/editCategory/EditCategory";

export const editCategoryRoute = createRoute({
  getParentRoute: () => appRoute,
  path: ROUTES.PRIVATE.ORGANIZATION_ITEM_CATALOG + ROUTES.PRIVATE.EDIT_CATEGORY,
  component: EditCategory,
  validateSearch: (search: Record<string, unknown>) => {
    // Ensure id is always a string and is required
    if (typeof search.id !== 'string' || !search.id) {
      throw new Error('Category ID is required and must be a string');
    }
    return {
      id: search.id,
    };
  },
});
