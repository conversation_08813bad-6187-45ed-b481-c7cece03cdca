import { createRoute } from "@tanstack/react-router";
import { ROUTES } from "@/constants/routes.constant";
import { appRoute } from "@/routes/private/app.route";
import DesignationDetails from "@/pages/private/designationDetails/DesignationDetails";

export const designationDetailsRoute = createRoute({
  getParentRoute: () => appRoute,
  path: ROUTES.PRIVATE.DESIGNATION_DETAILS,
  component: DesignationDetails,
});
