import { DOCUMENT_HEADER_PREFIX } from "@/constants/app.constant";
import { createRootRoute, HeadContent, Outlet } from "@tanstack/react-router";
import { TanStackRouterDevtoolsInProd } from '@tanstack/react-router-devtools'

export const rootRoute = createRootRoute({
    component: () => (
        <>
            <HeadContent />
            <Outlet />
            {import.meta.env.VITE_ENV === "development" && (
                <TanStackRouterDevtoolsInProd />
            )}
        </>
    ),
    head: () => ({
        meta: [
          {
            title: DOCUMENT_HEADER_PREFIX,
          },
        ],
      }),
});