import { createRoute, Navigate } from "@tanstack/react-router";
import { rootRoute } from "@/routes/root.route";
import { useAuth } from "@/hooks/useAuth";
import { ROUTES } from "@/constants/routes.constant";
import { loginRoute } from "./login.route";

export const indexRoute = createRoute({
    getParentRoute: () => rootRoute,
    path: "/",
    component: () => {
        const { isAuthenticated } = useAuth();
        
        if (isAuthenticated) {
            return <Navigate to={`${ROUTES.PRIVATE.APP}${ROUTES.PRIVATE.DASHBOARD}`} />;
        }
        
        return <Navigate to={loginRoute.to} />;
    },
}); 