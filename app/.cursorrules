Avinya Ops Frontend
Description: React frontend with TypeScript, Vite, PrimeReact, and TanStack Router

Rules:

1. Code Organization
   Description: Frontend code structure and organization rules
   Details:
   - Use functional components with TypeScript interfaces
   - Follow a modular, domain-oriented folder structure
   - Use path aliases (e.g., @/components, @/pages, etc.)
   - Implement lazy loading for routes using TanStack Router
   - Use React Query (TanStack Query) for data fetching
   - Structure files: exported component, subcomponents, helpers, static content, types
   - Favor named exports for components
   - Use descriptive variable names (e.g., isLoading, hasError)

2. Styling Guidelines
   Description: CSS and styling conventions
   Details:
   - Use PrimeReact's built-in classes for standard components
   - For custom styling, follow BEM (Block Element Modifier) convention
   - Block naming: Use kebab-case (e.g., 'user-card')
   - Element naming: Block__Element (e.g., 'user-card__header')
   - Modifier naming: Block--Modifier or Block__Element--Modifier (e.g., 'user-card--active' or 'user-card__header--collapsed')
   - Avoid nesting more than 3 levels deep in BEM
   - Use CSS Modules for component-specific styles
   - Maintain consistent spacing and typography using PrimeFlex

3. State Management
   Description: Rules for managing application state
   Details:
   - Use React Context for simple global state
   - Use React Query for server state and caching
   - Implement proper error boundaries for failure resilience
   - Use Redux Toolkit if complex local state requires it

4. Routing
   Description: Routing conventions with TanStack Router
   Details:
   - Use TanStack Router for route management
   - Apply lazy loading with `React.lazy` and `Suspense`
   - Organize routes in a centralized file or folder
   - Use nested routes for layout-based structure

5. TypeScript Usage
   Description: TypeScript-specific development rules
   Details:
   - Use TypeScript for all code
   - Prefer interfaces over types
   - Avoid enums; use object maps or union types instead
   - Avoid using `any`; always define proper types
   - Use function keyword for pure functions

6. Syntax and Formatting
   Description: General code style rules
   Details:
   - Do not remove comments or formatting unless necessary
   - Use curly braces for all conditionals
   - Write clean, readable, declarative JSX
   - Prefer immutability and avoid side effects in logic

7. Testing
   Description: Testing guidelines and requirements
   Details:
   - Write unit tests for reusable components
   - Implement integration tests for flows and features
   - Use Jest and React Testing Library
   - Maintain minimum 80% test coverage

8. Performance Optimization
   Description: Rules for optimizing app performance
   Details:
   - Use immutable data structures and memoization (e.g., React.memo, useMemo)
   - Optimize network requests using React Query
   - Minimize re-renders via component splitting and hooks
   - Avoid unnecessary state or prop drilling
   - Debounce input handlers where needed

9. PrimeReact Integration
   Description: Guidelines for using PrimeReact components
   Details:
   - Use PrimeReact's built-in classes when possible
   - Extend PrimeReact components using className prop
   - Follow PrimeReact's theming system for consistent styling
   - Use PrimeIcons for icons
   - Leverage PrimeFlex for layout and spacing

10. Component Styling Examples
    Description: Examples of proper styling combinations
    Details:
    - PrimeReact + BEM: <Button className='p-button user-form__submit--primary' />
    - CSS Module + BEM: import styles from './UserCard.module.css' -> className={styles['user-card']}
    - PrimeFlex + BEM: className='flex align-items-center user-list__item'
    - Avoid: Mixing multiple naming conventions in the same component