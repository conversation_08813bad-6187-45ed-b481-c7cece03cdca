{"name": "avinya-ops-frontend", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@tanstack/react-query": "^5.17.19", "@tanstack/react-router": "^1.119.0", "@tanstack/react-router-devtools": "^1.119.0", "axios": "^1.6.7", "primeflex": "^3.3.1", "primeicons": "^6.0.1", "primereact": "^10.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.56.1", "react-router-dom": "^6.22.1", "zod": "^3.24.3"}, "devDependencies": {"@chromatic-com/storybook": "^3", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-onboarding": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/experimental-addon-test": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/react-vite": "^8.6.12", "@storybook/test": "^8.6.12", "@types/node": "^20.11.19", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/browser": "^3.1.2", "@vitest/coverage-v8": "^3.1.2", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "eslint-plugin-storybook": "^0.12.0", "playwright": "^1.52.0", "storybook": "^8.6.12", "typescript": "^5.2.2", "vite": "^5.1.0", "vitest": "^3.1.2"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}