# Exception Handling in AvinyaOps

This document outlines the exception handling approach for the AvinyaOps application, focusing on how to create and use exceptions properly.

## Table of Contents

1. [Exception Hierarchy](#exception-hierarchy)
2. [Creating New Exceptions](#creating-new-exceptions)
3. [Best Practices](#best-practices)
4. [Examples](#examples)
5. [Error Response Format](#error-response-format)

## Exception Hierarchy

The application uses a structured exception hierarchy:

```
RuntimeException
├── AvinyaException (base exception with error code)
│   ├── ResourceNotFoundException
│   ├── FileUploadException
│   └── ... (other domain-specific exceptions)
└── Standard exceptions (direct RuntimeException extensions)
    ├── UserNotFoundException
    ├── EmployeeNotFoundException
    ├── ResourceInUseException
    └── ... (other simple exceptions)
```

## Creating New Exceptions

### Option 1: Extending AvinyaException

Use this approach for exceptions that need error codes and structured error handling:

```java
package com.avinyaops.procurement.your.module.exception;

import com.avinyaops.procurement.exception.AvinyaException;

public class YourCustomException extends AvinyaException {
    private static final String ERROR_CODE = "YOUR_ERROR_CODE";

    public YourCustomException(String resourceName, String identifier) {
        super(ERROR_CODE, String.format("%s failed with identifier: %s", resourceName, identifier));
    }

    public YourCustomException(String resourceName, String identifier, Throwable cause) {
        super(ERROR_CODE, String.format("%s failed with identifier: %s", resourceName, identifier), cause);
    }
}
```

### Option 2: Extending RuntimeException

Use this approach for simpler exceptions without error codes:

```java
package com.avinyaops.procurement.your.module.exception;

public class YourSimpleException extends RuntimeException {
    public YourSimpleException(String message) {
        super(message);
    }

    public YourSimpleException(String message, Throwable cause) {
        super(message, cause);
    }
}
```

## Best Practices

1. **Use Descriptive Names**: Name exceptions clearly to indicate what went wrong (e.g., `ResourceNotFoundException`, not just `NotFoundException`).

2. **Include Context**: Include relevant information in exception messages to aid debugging.

3. **Error Codes**: For `AvinyaException` subclasses, define a static error code constant.

4. **HTTP Status Mapping**: Use `@ResponseStatus` annotation for exceptions that map directly to HTTP status codes.

5. **Logging**: Always log exceptions at appropriate levels (ERROR for server errors, WARN for client errors).

6. **Exception Handling**: Handle exceptions at the appropriate level, typically in service or controller layers.

7. **Consistent Formatting**: Use consistent message formatting within each exception type.

## Examples

### Example 1: File Not Found Exception

```java
package com.avinyaops.procurement.fileupload.exception;

import com.avinyaops.procurement.exception.AvinyaException;

public class FileNotFoundException extends AvinyaException {
    private static final String ERROR_CODE = "FILE_NOT_FOUND";

    public FileNotFoundException(String fileId) {
        super(ERROR_CODE, String.format("File not found with ID: %s", fileId));
    }

    public FileNotFoundException(String fileId, Throwable cause) {
        super(ERROR_CODE, String.format("File not found with ID: %s", fileId), cause);
    }
}
```

### Example 2: Invalid File Type Exception

```java
package com.avinyaops.procurement.fileupload.exception;

import com.avinyaops.procurement.exception.AvinyaException;

public class InvalidFileTypeException extends AvinyaException {
    private static final String ERROR_CODE = "INVALID_FILE_TYPE";

    public InvalidFileTypeException(String fileName, String allowedTypes) {
        super(ERROR_CODE, String.format("File '%s' has invalid type. Allowed types: %s", fileName, allowedTypes));
    }

    public InvalidFileTypeException(String fileName, String allowedTypes, Throwable cause) {
        super(ERROR_CODE, String.format("File '%s' has invalid type. Allowed types: %s", fileName, allowedTypes), cause);
    }
}
```

### Example 3: Simple Exception

```java
package com.avinyaops.procurement.organization.exception;

public class ResourceInUseException extends RuntimeException {
    public ResourceInUseException(String message) {
        super(message);
    }
    
    public ResourceInUseException(String message, Throwable cause) {
        super(message, cause);
    }
}
```

## Error Response Format

The application uses a standardized error response format:

```json
{
  "timestamp": "2023-07-15T10:30:45.123Z",
  "errorCode": "RESOURCE_NOT_FOUND",
  "message": "User not found with identifier: <EMAIL>",
  "details": ["Additional error detail 1", "Additional error detail 2"],
  "path": "/api/v1/users/123",
  "traceId": "abc123def456"
}
```

This format is generated by the `ErrorResponse` class and handled by the `GlobalExceptionHandler`.

### Handling in GlobalExceptionHandler

To ensure your new exception is properly handled, you may need to add a specific handler in `GlobalExceptionHandler` if it requires special treatment:

```java
@ExceptionHandler(YourCustomException.class)
public ResponseEntity<ErrorResponse> handleYourCustomException(YourCustomException ex, WebRequest request) {
    log.error("Your Custom Exception: ", ex);
    ErrorResponse errorResponse = ErrorResponse.of(ex.getErrorCode(), ex.getMessage());
    return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST); // Or appropriate status
}
```

For most exceptions extending `AvinyaException`, the existing handler will work automatically.
